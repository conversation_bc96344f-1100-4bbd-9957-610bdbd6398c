'use client';

import { motion } from 'framer-motion';
import { Plus, Edit, Trash2, ExternalLink, Github, Calendar, FolderOpen, Star, GitFork } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';

import AdminProtectedLayout from '@/components/admin/AdminProtectedLayout';
import DraggableProjectList from '@/components/admin/DraggableProjectList';
import ErrorMessage from '@/components/admin/ErrorMessage';
import { LoadingCard } from '@/components/admin/LoadingSpinner';
import { ApiService } from '@/lib/api-service';
import type { Project } from '@/types';

export default function ProjectsManagement() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      // Use the ordered projects endpoint to get projects in their current order
      const response = await ApiService.getOrderedProjects();
      if (response.success && response.data) {
        setProjects(response.data.projects || []);
      } else {
        setError(response.error || 'Failed to fetch projects');
      }
    } catch (error) {
      console.error('Fetch projects error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const deleteProject = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this project?')) {
      return;
    }

    try {
      const response = await ApiService.deleteProject(id);

      if (response.success) {
        setProjects(projects.filter(p => p.id !== id));
      } else {
        setError(response.error || 'Failed to delete project');
      }
    } catch (error) {
      console.error('Delete project error:', error);
      setError('Network error. Please try again.');
    }
  };

  const handleReorder = async (projectIds: string[]) => {
    try {
      const response = await ApiService.reorderProjects(projectIds);
      if (!response.success) {
        throw new Error(response.error || 'Failed to reorder projects');
      }
    } catch (error) {
      console.error('Reorder projects error:', error);
      throw error; // Re-throw to let the DraggableProjectList handle the error
    }
  };

  const handleEdit = (project: Project) => {
    // Navigate to edit page - this will be handled by the DraggableProjectList
    window.location.href = `/admin/projects/${project.id}/edit`;
  };

  return (
    <AdminProtectedLayout title="Projects Management" subtitle="Manage your portfolio projects">
      <div className="bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link
              href="/admin/github"
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Github className="w-4 h-4 mr-2" />
              GitHub Sync
            </Link>
          </div>
          <Link
            href="/admin/projects/new"
            className="inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors glow-hover"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Project
          </Link>
        </div>
      </div>

      <div className="p-6">
        {error && (
          <ErrorMessage
            message={error}
            onRetry={fetchProjects}
            onDismiss={() => setError('')}
            className="mb-6"
          />
        )}

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        ) : projects.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/20 dark:to-primary-800/20 rounded-2xl flex items-center justify-center">
              <FolderOpen className="w-10 h-10 text-primary-600 dark:text-primary-400" />
            </div>
            <h3 className="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-3">
              No projects yet
            </h3>
            <p className="text-text-secondary-light dark:text-text-secondary-dark mb-8 max-w-md mx-auto">
              Get started by adding your first project to showcase your work.
            </p>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/admin/projects/new"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg font-semibold"
              >
                <Plus className="w-5 h-5 mr-2" />
                Add Your First Project
              </Link>
            </motion.div>
          </motion.div>
        ) : (
          <DraggableProjectList
            projects={projects}
            onEdit={handleEdit}
            onDelete={deleteProject}
            onReorder={handleReorder}
          />
        )}
      </div>
    </AdminProtectedLayout>
  );
}

import { NextRequest, NextResponse } from 'next/server';

import { getAuthenticatedUser } from '@/lib/auth-server';
import { getPortfolioData, savePortfolioData } from '@/lib/storage';
import type { Project } from '@/types';

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { projectIds } = await request.json();

    if (!Array.isArray(projectIds)) {
      return NextResponse.json(
        { error: 'Project IDs must be an array' },
        { status: 400 }
      );
    }

    // Get current portfolio data
    const portfolioData = await getPortfolioData();
    const projects = portfolioData.projects || [];

    // Validate that all provided IDs exist
    const existingIds = new Set(projects.map(p => p.id));
    const invalidIds = projectIds.filter(id => !existingIds.has(id));
    
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid project IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      );
    }

    // Check if all projects are included in the reorder
    if (projectIds.length !== projects.length) {
      return NextResponse.json(
        { error: 'All projects must be included in the reorder' },
        { status: 400 }
      );
    }

    // Create a map of projects by ID for efficient lookup
    const projectMap = new Map<string, Project>();
    projects.forEach(project => {
      projectMap.set(project.id, project);
    });

    // Reorder projects and assign new order values
    const reorderedProjects: Project[] = projectIds.map((id: string, index: number) => {
      const project = projectMap.get(id)!;
      return {
        ...project,
        order: index,
        updatedAt: new Date().toISOString(),
      };
    });

    // Save the reordered projects
    const updatedPortfolioData = {
      ...portfolioData,
      projects: reorderedProjects,
    };

    const saveResult = await savePortfolioData(updatedPortfolioData);

    if (!saveResult.success) {
      return NextResponse.json(
        { error: 'Failed to save project order' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Projects reordered successfully',
      projects: reorderedProjects,
    });
  } catch (error) {
    console.error('Error reordering projects:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve projects in their current order
export async function GET() {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const portfolioData = await getPortfolioData();
    const projects = portfolioData.projects || [];

    // Sort projects by order field, with fallback to creation date
    const sortedProjects = [...projects].sort((a, b) => {
      // If both have order, sort by order
      if (typeof a.order === 'number' && typeof b.order === 'number') {
        return a.order - b.order;
      }
      
      // If only one has order, prioritize it
      if (typeof a.order === 'number') return -1;
      if (typeof b.order === 'number') return 1;
      
      // If neither has order, sort by creation date (newest first)
      const aDate = new Date(a.createdAt || 0).getTime();
      const bDate = new Date(b.createdAt || 0).getTime();
      return bDate - aDate;
    });

    return NextResponse.json({
      success: true,
      projects: sortedProjects,
    });
  } catch (error) {
    console.error('Error fetching ordered projects:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

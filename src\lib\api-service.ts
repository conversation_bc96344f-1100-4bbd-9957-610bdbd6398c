import type {
  Project,
  ContactForm,
  ApiResponse,
  PersonalInfo,
  ContactMessage,
  AdminUser,
  PortfolioData,
  ProjectForm,
} from '@/types';

import { api } from './api-client';

// Login credentials type
export interface LoginCredentials {
  email: string;
  password: string;
}

/**
 * Centralized API Service
 * Uses the robust api-client.ts for all API calls with proper error handling,
 * retries, timeouts, and TypeScript typing
 */
export class ApiService {
  // ==================== PUBLIC ENDPOINTS ====================
  
  /**
   * Submit contact form
   */
  static async submitContact(data: ContactForm): Promise<ApiResponse<{ message: string }>> {
    return api.post<{ message: string }>('/api/contact', data);
  }

  /**
   * Get portfolio data (public)
   */
  static async getPortfolio(): Promise<ApiResponse<PortfolioData>> {
    return api.get<PortfolioData>('/api/portfolio');
  }

  /**
   * Get projects (public)
   */
  static async getProjects(): Promise<ApiResponse<{ projects: Project[] }>> {
    return api.get<{ projects: Project[] }>('/api/projects');
  }

  // ==================== ADMIN AUTH ENDPOINTS ====================
  
  /**
   * Admin login
   */
  static async adminLogin(credentials: LoginCredentials): Promise<ApiResponse<{ 
    success: boolean; 
    user: AdminUser 
  }>> {
    return api.post<{ success: boolean; user: AdminUser }>('/api/admin/auth/login', credentials);
  }

  /**
   * Admin logout
   */
  static async adminLogout(): Promise<ApiResponse<{ success: boolean }>> {
    return api.post<{ success: boolean }>('/api/admin/auth/logout');
  }

  /**
   * Get current admin user
   */
  static async getAdminUser(): Promise<ApiResponse<{ user: AdminUser }>> {
    return api.get<{ user: AdminUser }>('/api/admin/auth/me');
  }

  // ==================== ADMIN PORTFOLIO ENDPOINTS ====================
  
  /**
   * Get portfolio data (admin)
   */
  static async getAdminPortfolio(): Promise<ApiResponse<PortfolioData>> {
    return api.get<PortfolioData>('/api/admin/portfolio');
  }

  /**
   * Update portfolio data
   */
  static async updatePortfolio(data: Partial<PortfolioData>): Promise<ApiResponse<PortfolioData>> {
    return api.put<PortfolioData>('/api/admin/portfolio', data);
  }

  // ==================== ADMIN PROJECTS ENDPOINTS ====================
  
  /**
   * Get projects (admin)
   */
  static async getAdminProjects(): Promise<ApiResponse<{ projects: Project[] }>> {
    return api.get<{ projects: Project[] }>('/api/admin/projects');
  }

  /**
   * Create new project
   */
  static async createProject(project: ProjectForm): Promise<ApiResponse<{
    success: boolean;
    project: Project
  }>> {
    return api.post<{ success: boolean; project: Project }>('/api/admin/projects', project);
  }

  /**
   * Update project
   */
  static async updateProject(id: string, updates: Partial<Project>): Promise<ApiResponse<{ 
    success: boolean; 
    project: Project 
  }>> {
    return api.put<{ success: boolean; project: Project }>(`/api/admin/projects/${id}`, updates);
  }

  /**
   * Delete project
   */
  static async deleteProject(id: string): Promise<ApiResponse<{ success: boolean }>> {
    return api.delete<{ success: boolean }>(`/api/admin/projects/${id}`);
  }

  /**
   * Reorder projects
   */
  static async reorderProjects(projectIds: string[]): Promise<ApiResponse<{ success: boolean; projects: any[] }>> {
    return api.put<{ success: boolean; projects: any[] }>('/api/admin/projects/reorder', { projectIds });
  }

  /**
   * Get projects in their current order
   */
  static async getOrderedProjects(): Promise<ApiResponse<{ success: boolean; projects: any[] }>> {
    return api.get<{ success: boolean; projects: any[] }>('/api/admin/projects/reorder');
  }

  /**
   * Reorder experience
   */
  static async reorderExperience(experienceIds: string[]): Promise<ApiResponse<{ success: boolean; experience: any[] }>> {
    return api.put<{ success: boolean; experience: any[] }>('/api/admin/experience/reorder', { experienceIds });
  }

  /**
   * Get experience in their current order
   */
  static async getOrderedExperience(): Promise<ApiResponse<{ success: boolean; experience: any[] }>> {
    return api.get<{ success: boolean; experience: any[] }>('/api/admin/experience/reorder');
  }

  /**
   * Reorder education
   */
  static async reorderEducation(educationIds: string[]): Promise<ApiResponse<{ success: boolean; education: any[] }>> {
    return api.put<{ success: boolean; education: any[] }>('/api/admin/education/reorder', { educationIds });
  }

  /**
   * Get education in their current order
   */
  static async getOrderedEducation(): Promise<ApiResponse<{ success: boolean; education: any[] }>> {
    return api.get<{ success: boolean; education: any[] }>('/api/admin/education/reorder');
  }

  /**
   * Upload story image
   */
  static async uploadStoryImage(file: File): Promise<ApiResponse<{ success: boolean; url: string; filename: string; size: number; type: string }>> {
    const formData = new FormData();
    formData.append('file', file);

    // Use fetch directly for file uploads to ensure proper Content-Type handling
    try {
      const response = await fetch('/api/admin/upload/story-image', {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header - let the browser set it automatically for FormData
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP error! status: ${response.status}`,
          data: null,
        };
      }

      return {
        success: true,
        data,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
        data: null,
      };
    }
  }

  /**
   * Delete story image
   */
  static async deleteStoryImage(filename: string): Promise<ApiResponse<{ success: boolean }>> {
    try {
      const response = await fetch(`/api/admin/upload/story-image?filename=${encodeURIComponent(filename)}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP error! status: ${response.status}`,
          data: null,
        };
      }

      return {
        success: true,
        data,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed',
        data: null,
      };
    }
  }

  // ==================== ADMIN CONTACTS ENDPOINTS ====================
  
  /**
   * Get contact messages
   */
  static async getContactMessages(): Promise<ApiResponse<{ messages: ContactMessage[] }>> {
    return api.get<{ messages: ContactMessage[] }>('/api/admin/contacts');
  }

  /**
   * Mark message as read
   */
  static async markMessageAsRead(id: string): Promise<ApiResponse<{ success: boolean }>> {
    return api.patch<{ success: boolean }>(`/api/admin/contacts/${id}`, { read: true });
  }

  /**
   * Delete contact message
   */
  static async deleteContactMessage(id: string): Promise<ApiResponse<{ success: boolean }>> {
    return api.delete<{ success: boolean }>(`/api/admin/contacts/${id}`);
  }

  /**
   * Bulk delete contact messages
   */
  static async bulkDeleteMessages(messageIds: string[]): Promise<ApiResponse<{ success: boolean }>> {
    return api.post<{ success: boolean }>('/api/admin/contacts/bulk-delete', { messageIds });
  }

  // GitHub Integration Methods

  /**
   * Fetch GitHub repositories without syncing
   */
  static async fetchGitHubRepositories(params: {
    username: string;
    accessToken?: string;
    includeForked?: boolean;
    includePrivate?: boolean;
  }): Promise<ApiResponse<{ repositories: any[] }>> {
    const searchParams = new URLSearchParams();
    searchParams.set('username', params.username);
    if (params.accessToken) searchParams.set('accessToken', params.accessToken);
    if (params.includeForked) searchParams.set('includeForked', 'true');
    if (params.includePrivate) searchParams.set('includePrivate', 'true');

    return api.get<{ repositories: any[] }>(`/api/admin/github/sync?${searchParams}`);
  }

  /**
   * Sync projects from GitHub repositories
   */
  static async syncGitHubProjects(params: {
    username: string;
    accessToken?: string;
    includeForked?: boolean;
    includePrivate?: boolean;
    selectedRepos?: string[];
  }): Promise<ApiResponse<any>> {
    return api.post<any>('/api/admin/github/sync', params);
  }

  /**
   * Get GitHub configuration
   */
  static async getGitHubConfig(): Promise<ApiResponse<any>> {
    return api.get<any>('/api/admin/github/config');
  }

  /**
   * Update GitHub configuration
   */
  static async updateGitHubConfig(config: any): Promise<ApiResponse<any>> {
    return api.put<any>('/api/admin/github/config', config);
  }

  /**
   * Trigger auto-sync
   */
  static async triggerAutoSync(): Promise<ApiResponse<any>> {
    return api.post<any>('/api/admin/github/auto-sync', {});
  }

  /**
   * Get auto-sync status
   */
  static async getAutoSyncStatus(): Promise<ApiResponse<any>> {
    return api.get<any>('/api/admin/github/auto-sync');
  }
}

// Export individual methods for convenience
export const {
  // Public API
  submitContact,
  getPortfolio,
  getProjects,

  // Admin Auth
  adminLogin,
  adminLogout,
  getAdminUser,

  // Admin Portfolio
  getAdminPortfolio,
  updatePortfolio,

  // Admin Projects
  getAdminProjects,
  createProject,
  updateProject,
  deleteProject,
  reorderProjects,
  getOrderedProjects,

  // Admin Experience & Education
  reorderExperience,
  getOrderedExperience,
  reorderEducation,
  getOrderedEducation,

  // Admin Story Image Upload
  uploadStoryImage,
  deleteStoryImage,

  // Admin Contacts
  getContactMessages,
  markMessageAsRead,
  deleteContactMessage,
  bulkDeleteMessages,

  // GitHub Integration
  fetchGitHubRepositories,
  syncGitHubProjects,
  getGitHubConfig,
  updateGitHubConfig,
  triggerAutoSync,
  getAutoSyncStatus,
} = ApiService;

// Default export
export default ApiService;

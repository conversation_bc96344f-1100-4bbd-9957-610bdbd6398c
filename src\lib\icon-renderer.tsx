import {
  // Programming & Development
  Code2, Code, Terminal, FileCode, GitBranch, Github, Database, Server, Cloud, 
  // Frontend Technologies
  Palette, Brush, Layout, Monitor, Tablet, Globe, Eye,
  // Backend & Tools
  Wrench, Settings, Cog, Package, Box, Layers, Component,
  // Languages & Frameworks
  Languages, Type, Hash, Brackets, Braces, 
  // AI & Machine Learning
  Brain, Bot, Cpu, Zap, Sparkles, Target, TrendingUp, BarChart,
  // Data & Analytics
  PieChart, LineChart, BarChart3, Activity, Gauge, 
  // Design & UI
  Figma, Paintbrush, Image, Camera, Video, Music,
  // Mobile & Devices
  Smartphone, Watch, Headphones, Gamepad2,
  // Cloud & DevOps
  CloudLightning, Shield, Lock, Key, Wifi, Network,
  // General Tech
  Lightbulb, Rocket, Star, Award, Trophy, Medal,
  // Default fallback
  HelpCircle,
} from 'lucide-react';

// Icon mapping object
const iconMap = {
  // Programming & Development
  'code2': Code2,
  'code': Code,
  'terminal': Terminal,
  'file-code': FileCode,
  'git-branch': GitBranch,
  'github': Github,
  'database': Database,
  'server': Server,
  'cloud': Cloud,
  
  // Frontend Technologies
  'palette': Palette,
  'brush': Brush,
  'layout': Layout,
  'monitor': Monitor,
  'tablet': Tablet,
  'globe': Globe,
  'eye': Eye,
  
  // Backend & Tools
  'wrench': Wrench,
  'settings': Settings,
  'cog': Cog,
  'package': Package,
  'box': Box,
  'layers': Layers,
  'component': Component,
  
  // Languages & Frameworks
  'languages': Languages,
  'type': Type,
  'hash': Hash,
  'brackets': Brackets,
  'braces': Braces,
  
  // AI & Machine Learning
  'brain': Brain,
  'bot': Bot,
  'cpu': Cpu,
  'zap': Zap,
  'sparkles': Sparkles,
  'target': Target,
  'trending-up': TrendingUp,
  
  // Data & Analytics
  'bar-chart': BarChart,
  'pie-chart': PieChart,
  'line-chart': LineChart,
  'bar-chart-3': BarChart3,
  'activity': Activity,
  'gauge': Gauge,
  
  // Design & UI
  'figma': Figma,
  'paintbrush': Paintbrush,
  'image': Image,
  'camera': Camera,
  'video': Video,
  'music': Music,
  
  // Mobile & Devices
  'smartphone': Smartphone,
  'watch': Watch,
  'headphones': Headphones,
  'gamepad-2': Gamepad2,
  
  // Cloud & DevOps
  'cloud-lightning': CloudLightning,
  'shield': Shield,
  'lock': Lock,
  'key': Key,
  'wifi': Wifi,
  'network': Network,
  
  // General Tech
  'lightbulb': Lightbulb,
  'rocket': Rocket,
  'star': Star,
  'award': Award,
  'trophy': Trophy,
  'medal': Medal,
};

interface IconRendererProps {
  iconName?: string;
  className?: string;
  size?: number;
  color?: string;
}

export function IconRenderer({ iconName, className = '', size = 20, color }: IconRendererProps) {
  // Get the icon component from the map, fallback to HelpCircle if not found
  const IconComponent = iconName ? iconMap[iconName as keyof typeof iconMap] || HelpCircle : HelpCircle;
  
  return (
    <IconComponent 
      className={className} 
      size={size}
      style={color ? { color } : undefined}
    />
  );
}

export default IconRenderer;

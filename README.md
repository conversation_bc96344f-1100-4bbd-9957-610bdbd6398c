# Portfolio Website

A modern, high-performance portfolio website built with Next.js 15, TypeScript, and Tailwind CSS. Features comprehensive accessibility, SEO optimization, security measures, and development tooling.

## 🚀 Features

### Core Features
- **Modern Stack**: Next.js 15, React 19, TypeScript 5
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Dark/Light Mode**: System preference detection with manual toggle
- **Admin Panel**: Content management system with authentication
- **Contact Form**: Integrated contact form with validation

### Performance Optimizations
- **Bundle Optimization**: Code splitting and lazy loading
- **Image Optimization**: Next.js Image component with WebP/AVIF support
- **Core Web Vitals**: Optimized for LCP, FID, CLS metrics
- **Caching**: Aggressive caching strategies
- **Preloading**: Route and resource preloading

### Accessibility (WCAG 2.1 AA)
- **Screen Reader Support**: Comprehensive ARIA labels and live regions
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus trapping and restoration
- **Reduced Motion**: Respects user motion preferences
- **Color Contrast**: WCAG AA compliant color schemes

### Security
- **Input Sanitization**: XSS and injection protection
- **Security Headers**: CSP, HSTS, and other security headers
- **Authentication**: Secure JWT-based admin authentication
- **Rate Limiting**: Client-side rate limiting
- **CSRF Protection**: Cross-site request forgery protection

### SEO & Structured Data
- **Meta Tags**: Comprehensive meta tag optimization
- **Open Graph**: Social media sharing optimization
- **JSON-LD**: Structured data for search engines
- **Sitemap**: Dynamic sitemap generation
- **Robots.txt**: Search engine crawling optimization

### Development Experience
- **TypeScript**: Strict type checking with enhanced configuration
- **ESLint**: Comprehensive linting rules
- **Prettier**: Code formatting
- **Husky**: Git hooks for code quality
- **Bundle Analyzer**: Bundle size analysis

## 🛠 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 3
- **UI Components**: Custom components with Framer Motion
- **Icons**: Lucide React & Heroicons
- **Forms**: React Hook Form with Zod validation
- **Authentication**: JWT with bcryptjs
- **State Management**: React Context API
- **Development**: ESLint, Prettier, Husky

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:
   ```env
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   JWT_SECRET=your-super-secret-jwt-key
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD_HASH=your-bcrypt-hash
   ```

4. **Generate admin password hash**
   ```bash
   npm run generate-password-hash
   ```

## 🚀 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking
- `npm run format` - Format code with Prettier
- `npm run analyze` - Analyze bundle size
- `npm run validate` - Run all validation checks

## 🔗 GitHub Integration

This portfolio includes powerful GitHub automation features to sync your repositories as portfolio projects.

### Features

- **Manual Sync**: Fetch and select repositories to sync as projects
- **Auto-Sync**: Automatically sync projects at regular intervals
- **Smart Filtering**: Include/exclude repositories based on criteria
- **GitHub Metadata**: Display stars, forks, languages, and topics
- **Live Updates**: Keep project data synchronized with GitHub

### Setup

1. **Access GitHub Sync**: Navigate to `/admin/github` in the admin panel
2. **Configure Settings**:
   - Enter your GitHub username
   - Optionally add a Personal Access Token for private repos and higher rate limits
   - Configure sync preferences (forked repos, private repos)
3. **Manual Sync**: Fetch repositories and select which ones to sync
4. **Auto-Sync** (Optional):
   - Enable automatic syncing
   - Set sync interval (hours)
   - Configure API key for external cron services

### GitHub Personal Access Token

For private repositories and higher rate limits, create a GitHub Personal Access Token:

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate a new token with `repo` scope
3. Add the token to your GitHub sync configuration

### External Auto-Sync

For production deployments, you can trigger auto-sync externally:

```bash
# Using API key (set GITHUB_SYNC_API_KEY environment variable)
curl -X POST https://your-domain.com/api/admin/github/auto-sync \
  -H "X-API-Key: your-api-key"

# Or use a cron service like GitHub Actions, Vercel Cron, or similar
```

### Environment Variables

```env
# Optional: API key for external auto-sync triggers
GITHUB_SYNC_API_KEY=your-secret-api-key
```

### Development Workflow

1. **Start development server**
   ```bash
   npm run dev
   ```

2. **Make changes** - The app will auto-reload

3. **Run validation** before committing
   ```bash
   npm run validate
   ```

4. **Commit changes** - Husky will run pre-commit hooks

## 🏗 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── layout/           # Layout components
│   ├── sections/         # Page sections
│   └── ui/               # Reusable UI components
├── lib/                  # Utility libraries
│   ├── accessibility.ts  # Accessibility utilities
│   ├── api-client.ts     # API client with error handling
│   ├── auth.ts           # Authentication utilities
│   ├── component-utils.ts # Component utilities
│   ├── security.ts       # Security utilities
│   ├── seo.ts            # SEO and structured data
│   └── utils.ts          # General utilities
├── types/                # TypeScript type definitions
└── data/                 # Static data and configuration
```

## 🔧 Configuration

### TypeScript Configuration
- Strict type checking enabled
- Path mapping for clean imports
- Enhanced compiler options for better DX

### ESLint Configuration
- Next.js recommended rules
- TypeScript-specific rules
- Accessibility rules (jsx-a11y)
- Import/export rules
- Custom rules for code quality

### Security Configuration
- Content Security Policy (CSP)
- Security headers in Next.js config
- Input sanitization utilities
- Authentication middleware

## 📊 Performance

### Core Web Vitals Targets
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1

### Optimization Techniques
- Image optimization with next/image
- Code splitting with dynamic imports
- Resource preloading and prefetching
- Bundle size optimization
- Caching strategies

## ♿ Accessibility

### WCAG 2.1 AA Compliance
- Semantic HTML structure
- Proper heading hierarchy
- Alt text for images
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance
- Focus management
- Reduced motion support

## 🔒 Security

### Security Measures
- Input sanitization and validation
- XSS protection
- CSRF protection
- Security headers
- Rate limiting
- Secure authentication
- Environment variable protection

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

### Manual Deployment
1. Build the application
   ```bash
   npm run build
   ```
2. Start the production server
   ```bash
   npm run start
   ```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run validation checks
5. Submit a pull request

## 📞 Support

For support, please open an issue in the GitHub repository or contact the maintainer.

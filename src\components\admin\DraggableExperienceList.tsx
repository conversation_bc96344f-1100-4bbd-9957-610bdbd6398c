'use client';

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Edit, 
  Trash2, 
  GripVertical,
  AlertCircle,
  CheckCircle,
  Undo2,
  Briefcase,
} from 'lucide-react';
import { useState, useEffect } from 'react';

import { ApiService } from '@/lib/api-service';
import type { Experience } from '@/types';

interface DraggableExperienceCardProps {
  experience: Experience;
  onEdit: (experience: Experience) => void;
  onDelete: (experienceId: string) => void;
  isDragging?: boolean;
}

function DraggableExperienceCard({ experience, onEdit, onDelete, isDragging = false }: DraggableExperienceCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: experience.id! });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200 ${
        isSortableDragging ? 'shadow-2xl scale-105 z-50' : 'hover:shadow-lg'
      } ${isDragging ? 'ring-2 ring-primary-500 ring-opacity-50' : ''}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg z-10 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        title="Drag to reorder experience"
        aria-label={`Drag to reorder ${experience.position} at ${experience.company}`}
        role="button"
        tabIndex={0}
      >
        <GripVertical className="w-4 h-4 text-gray-400 dark:text-gray-500" />
      </div>

      <div className="p-6 pl-12">
        <div className="flex items-start justify-between mb-2">
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {experience.position}
              </h3>
              {/* Type Badge */}
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                experience.type === 'work' 
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                  : experience.type === 'internship'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : experience.type === 'freelance'
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
              }`}>
                {experience.type}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              {experience.company}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              {experience.duration}
            </p>
          </div>

          <div className="flex items-start space-x-2 ml-4">
            <button
              onClick={() => onEdit(experience)}
              className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
              title="Edit experience"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(experience.id!)}
              className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
              title="Delete experience"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="mb-3">
          {experience.description.slice(0, 2).map((desc, index) => (
            <p key={index} className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              • {desc}
            </p>
          ))}
          {experience.description.length > 2 && (
            <p className="text-sm text-gray-500 dark:text-gray-500">
              +{experience.description.length - 2} more responsibilities
            </p>
          )}
        </div>

        <div className="flex flex-wrap gap-1">
          {experience.technologies.slice(0, 4).map((tech) => (
            <span
              key={tech}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs"
            >
              {tech}
            </span>
          ))}
          {experience.technologies.length > 4 && (
            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded text-xs">
              +{experience.technologies.length - 4} more
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

interface DraggableExperienceListProps {
  experience: Experience[];
  onEdit: (experience: Experience) => void;
  onDelete: (experienceId: string) => void;
  onReorder: (experienceIds: string[]) => Promise<void>;
}

export default function DraggableExperienceList({ 
  experience, 
  onEdit, 
  onDelete, 
  onReorder 
}: DraggableExperienceListProps) {
  const [items, setItems] = useState<Experience[]>(experience);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [isReordering, setIsReordering] = useState(false);
  const [reorderError, setReorderError] = useState<string | null>(null);
  const [reorderSuccess, setReorderSuccess] = useState(false);
  const [previousOrder, setPreviousOrder] = useState<Experience[]>([]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setItems(experience);
  }, [experience]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id);
    setPreviousOrder([...items]);
    setReorderError(null);
    setReorderSuccess(false);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over?.id);

      const newItems = arrayMove(items, oldIndex, newIndex);
      setItems(newItems);

      // Save the new order to the backend
      setIsReordering(true);
      try {
        await onReorder(newItems.map(item => item.id!));
        setReorderSuccess(true);
        setTimeout(() => setReorderSuccess(false), 3000);
      } catch (error) {
        console.error('Failed to reorder experience:', error);
        setReorderError('Failed to save new order. Please try again.');
        // Revert to previous order on error
        setItems(previousOrder);
        setTimeout(() => setReorderError(null), 5000);
      } finally {
        setIsReordering(false);
      }
    }

    setActiveId(null);
  };

  const handleUndo = () => {
    if (previousOrder.length > 0) {
      setItems(previousOrder);
      onReorder(previousOrder.map(item => item.id!)).catch(console.error);
      setPreviousOrder([]);
      setReorderError(null);
    }
  };

  const activeExperience = activeId ? items.find(item => item.id === activeId) : null;

  return (
    <div className="space-y-4">
      {/* Helper Text */}
      {items.length > 1 && (
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-3">
          <GripVertical className="w-4 h-4 text-purple-500" />
          <span>
            Drag the grip handle on the left of each experience card to reorder your experience. 
            The new order will be saved automatically and reflected on your public portfolio.
          </span>
        </div>
      )}

      {/* Status Messages */}
      <AnimatePresence>
        {isReordering && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-purple-600 dark:text-purple-400 text-sm"
          >
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600 dark:border-purple-400"></div>
            <span>Saving new order...</span>
          </motion.div>
        )}

        {reorderSuccess && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-green-600 dark:text-green-400 text-sm"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Experience reordered successfully!</span>
          </motion.div>
        )}

        {reorderError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center justify-between bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3"
          >
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{reorderError}</span>
            </div>
            {previousOrder.length > 0 && (
              <button
                onClick={handleUndo}
                className="flex items-center space-x-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium"
              >
                <Undo2 className="w-3 h-3" />
                <span>Undo</span>
              </button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items.map(item => item.id!)} strategy={verticalListSortingStrategy}>
          <div className="space-y-4">
            {items.map((exp) => (
              <DraggableExperienceCard
                key={exp.id}
                experience={exp}
                onEdit={onEdit}
                onDelete={onDelete}
                isDragging={activeId === exp.id}
              />
            ))}
          </div>
        </SortableContext>

        <DragOverlay>
          {activeExperience ? (
            <DraggableExperienceCard
              experience={activeExperience}
              onEdit={onEdit}
              onDelete={onDelete}
              isDragging={true}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {items.length === 0 && (
        <div className="text-center py-12 text-gray-500 dark:text-gray-400">
          <Briefcase className="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
          <p>No experience found. Add your first experience to get started!</p>
        </div>
      )}
    </div>
  );
}

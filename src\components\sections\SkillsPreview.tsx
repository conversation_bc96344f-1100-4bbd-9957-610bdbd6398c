'use client';

import { motion, useAnimation } from 'framer-motion';
import {
  <PERSON>R<PERSON>,
  Code2,
  Database,
  Wrench,
  Palette,
  Languages,
  MoreHorizontal,
  Zap,
  Star,
  Sparkles,
  Brain,
  Bo<PERSON>,
} from 'lucide-react';
import Link from 'next/link';
import React, { useState, useEffect } from 'react';

// import { skills } from '@/data/portfolio';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { ApiService } from '@/lib/api-service';
import type { Skill } from '@/types';

const categoryIcons = {
  frontend: Palette,
  backend: Code2,
  database: Database,
  tools: Wrench,
  languages: Languages,
  ai: Brain,
  'machine-learning': Bot,
  other: MoreHorizontal,
};

const categoryColors = {
  frontend: {
    primary: 'text-pink-500',
    bg: 'bg-pink-500/10',
    border: 'border-pink-500/30',
    glow: 'shadow-pink-500/25',
    gradient: 'from-pink-500/20 to-purple-500/20',
  },
  backend: {
    primary: 'text-blue-500',
    bg: 'bg-blue-500/10',
    border: 'border-blue-500/30',
    glow: 'shadow-blue-500/25',
    gradient: 'from-blue-500/20 to-cyan-500/20',
  },
  database: {
    primary: 'text-green-500',
    bg: 'bg-green-500/10',
    border: 'border-green-500/30',
    glow: 'shadow-green-500/25',
    gradient: 'from-green-500/20 to-emerald-500/20',
  },
  tools: {
    primary: 'text-orange-500',
    bg: 'bg-orange-500/10',
    border: 'border-orange-500/30',
    glow: 'shadow-orange-500/25',
    gradient: 'from-orange-500/20 to-yellow-500/20',
  },
  languages: {
    primary: 'text-purple-500',
    bg: 'bg-purple-500/10',
    border: 'border-purple-500/30',
    glow: 'shadow-purple-500/25',
    gradient: 'from-purple-500/20 to-indigo-500/20',
  },
  other: {
    primary: 'text-gray-500',
    bg: 'bg-gray-500/10',
    border: 'border-gray-500/30',
    glow: 'shadow-gray-500/25',
    gradient: 'from-gray-500/20 to-slate-500/20',
  },
};

export default function SkillsPreview() {
  const [skills, setSkills] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        const response = await ApiService.getPortfolio();
        if (response.success && response.data) {
          setSkills(response.data.skills || []);
        } else {
          console.error('Failed to fetch skills:', response.error);
          setSkills([]);
        }
      } catch (error) {
        console.error('Failed to fetch skills:', error);
        setSkills([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSkills();
  }, []);

  // Group skills by category and take top skills from each
  const skillsByCategory = skills.reduce(
    (acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = [];
      }
      acc[skill.category].push(skill);
      return acc;
    },
    {} as Record<string, typeof skills>,
  );

  // Get top 3 skills from each category
  const topSkillsByCategory = Object.entries(skillsByCategory).map(
    ([category, categorySkills]) => ({
      category,
      skills: (categorySkills as Skill[])
        .sort((a, b) => {
          const levelOrder = { expert: 4, advanced: 3, intermediate: 2, beginner: 1 };
          return levelOrder[b.level] - levelOrder[a.level];
        })
        .slice(0, 3),
    }),
  );

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 15,
      },
    },
  };

  const skillLevelVariants = {
    hidden: { width: 0, opacity: 0 },
    visible: (level: number) => ({
      width: `${(level / 4) * 100}%`,
      opacity: 1,
      transition: {
        duration: 1.2,
        delay: 0.5,
        ease: 'easeOut',
      },
    }),
  };

  const floatingVariants = {
    animate: {
      y: [-5, 5, -5],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut' as const,
      },
    },
  };

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
            Skills & Technologies
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            A glimpse into my technical toolkit and the technologies I work with
          </p>
        </motion.div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-pulse text-gray-500 dark:text-gray-400">Loading skills...</div>
          </div>
        ) : skills.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              No skills added yet. Add your skills through the admin panel.
            </p>
          </div>
        ) : (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-16"
          >
            {topSkillsByCategory.map(({ category, skills: categorySkills }, index) => {
              const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
              const colorConfig = categoryColors[category as keyof typeof categoryColors];

              return (
                <motion.div
                  key={category}
                  variants={itemVariants}
                  whileHover={{
                    y: -8,
                    scale: 1.02,
                    transition: { type: 'spring', stiffness: 300, damping: 20 },
                  }}
                  className="group"
                >
                  <div
                    className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${colorConfig.gradient} backdrop-blur-sm border ${colorConfig.border} transition-all duration-500 group-hover:shadow-2xl group-hover:${colorConfig.glow} h-full`}
                  >
                    {/* Floating particles background */}
                    <div className="absolute inset-0 overflow-hidden">
                      {[...Array(6)].map((_, i) => (
                        <motion.div
                          key={i}
                          className={`absolute w-1 h-1 ${colorConfig.primary.replace('text-', 'bg-')} rounded-full opacity-30`}
                          style={{
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                          }}
                          animate={{
                            y: [-10, 10, -10],
                            x: [-5, 5, -5],
                            opacity: [0.3, 0.7, 0.3],
                          }}
                          transition={{
                            duration: 3 + Math.random() * 2,
                            repeat: Infinity,
                            delay: Math.random() * 2,
                          }}
                        />
                      ))}
                    </div>

                    {/* Glowing border effect */}
                    <div
                      className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${colorConfig.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500`}
                    />

                    <div className="relative p-6 h-full flex flex-col">
                      {/* Header with floating icon */}
                      <div className="flex items-center space-x-4 mb-6">
                        <motion.div
                          className={`relative p-3 rounded-xl ${colorConfig.bg} ${colorConfig.border} border backdrop-blur-sm`}
                          variants={floatingVariants}
                          animate="animate"
                        >
                          <IconComponent
                            size={28}
                            className={`${colorConfig.primary} drop-shadow-lg`}
                          />

                          {/* Pulsing glow effect */}
                          <motion.div
                            className={`absolute inset-0 rounded-xl ${colorConfig.bg} opacity-50`}
                            animate={{
                              scale: [1, 1.1, 1],
                              opacity: [0.5, 0.8, 0.5],
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: 'easeInOut',
                            }}
                          />
                        </motion.div>

                        <div>
                          <h3 className="text-xl font-bold capitalize text-gray-900 dark:text-white mb-1">
                            {category}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {categorySkills.length} skill{categorySkills.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>

                      {/* Skills with innovative level indicators */}
                      <div className="space-y-4 flex-1">
                        {categorySkills.map((skill, skillIndex) => {
                          const levelValue = {
                            beginner: 1,
                            intermediate: 2,
                            advanced: 3,
                            expert: 4,
                          }[skill.level];

                          return (
                            <motion.div
                              key={skill.name}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: 0.7 + skillIndex * 0.1 }}
                              className="group/skill"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                  {skill.name}
                                </span>
                                <div className="flex items-center space-x-1">
                                  {skill.level === 'expert' && (
                                    <motion.div
                                      animate={{ rotate: 360 }}
                                      transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                                    >
                                      <Star
                                        size={12}
                                        className={`${colorConfig.primary} fill-current`}
                                      />
                                    </motion.div>
                                  )}
                                  <span className="text-xs font-medium text-gray-600 dark:text-gray-400 capitalize">
                                    {skill.level}
                                  </span>
                                </div>
                              </div>

                              {/* Hexagonal skill level indicator */}
                              <div className="relative">
                                <div className="flex space-x-1">
                                  {[1, 2, 3, 4].map(level => (
                                    <motion.div
                                      key={level}
                                      className="relative flex-1 h-2 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700"
                                      initial={{ scaleX: 0 }}
                                      animate={{ scaleX: 1 }}
                                      transition={{ delay: 1 + skillIndex * 0.1 + level * 0.05 }}
                                    >
                                      <motion.div
                                        className={`h-full rounded-full ${
                                          level <= levelValue
                                            ? `${colorConfig.primary.replace('text-', 'bg-')} shadow-lg`
                                            : 'bg-transparent'
                                        }`}
                                        initial={{ width: 0 }}
                                        animate={{
                                          width: level <= levelValue ? '100%' : '0%',
                                        }}
                                        transition={{
                                          delay: 1.2 + skillIndex * 0.1 + level * 0.05,
                                          duration: 0.6,
                                          ease: 'easeOut',
                                        }}
                                      />

                                      {/* Shimmer effect for filled levels */}
                                      {level <= levelValue && (
                                        <motion.div
                                          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                                          animate={{ x: ['-100%', '100%'] }}
                                          transition={{
                                            duration: 1.5,
                                            repeat: Infinity,
                                            delay: 2 + skillIndex * 0.2,
                                            repeatDelay: 3,
                                          }}
                                        />
                                      )}
                                    </motion.div>
                                  ))}
                                </div>
                              </div>
                            </motion.div>
                          );
                        })}
                      </div>

                      {/* Skill count badge */}
                      <motion.div
                        className={`mt-4 inline-flex items-center space-x-2 px-3 py-1 rounded-full ${colorConfig.bg} ${colorConfig.border} border backdrop-blur-sm`}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Sparkles size={12} className={colorConfig.primary} />
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          Top {categorySkills.length} skills
                        </span>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        )}

        {/* Terminal-style code preview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-12"
        >
          <Card className="bg-gray-900 border-gray-700 overflow-hidden">
            <div className="flex items-center space-x-2 px-4 py-3 bg-gray-800 border-b border-gray-700">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <span className="text-sm text-gray-400 ml-4">~/portfolio/skills</span>
            </div>
            <div className="p-6 font-mono text-sm">
              <div className="text-green-400">
                <span className="text-gray-500">$</span> cat skills.json
              </div>
              <div className="mt-2 text-gray-300">
                <div className="text-blue-400">{'{'}</div>
                <div className="ml-4">
                  <span className="text-yellow-400">"frontend"</span>: [
                  <span className="text-green-300">"React"</span>,
                  <span className="text-green-300">"TypeScript"</span>,
                  <span className="text-green-300">"Next.js"</span>
                  ],
                </div>
                <div className="ml-4">
                  <span className="text-yellow-400">"backend"</span>: [
                  <span className="text-green-300">"Node.js"</span>,
                  <span className="text-green-300">"Python"</span>,
                  <span className="text-green-300">"Express"</span>
                  ],
                </div>
                <div className="ml-4">
                  <span className="text-yellow-400">"database"</span>: [
                  <span className="text-green-300">"PostgreSQL"</span>,
                  <span className="text-green-300">"MongoDB"</span>
                  ],
                </div>
                <div className="ml-4">
                  <span className="text-yellow-400">"status"</span>:
                  <span className="text-green-300">"always_learning"</span>
                </div>
                <div className="text-blue-400">{'}'}</div>
              </div>
              <div className="mt-4 text-green-400">
                <span className="text-gray-500">$</span>
                <span className="animate-blink border-r border-green-400 ml-1"></span>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* CTA to full skills page */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <Link href="/about">
            <Button
              variant="outline"
              size="lg"
              icon={<ArrowRight size={20} />}
              iconPosition="right"
            >
              View All Skills
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}

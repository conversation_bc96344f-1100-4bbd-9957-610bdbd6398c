export interface Project {
  readonly id: string;
  title: string;
  description: string;
  longDescription?: string;
  technologies: readonly string[];
  category: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'other';
  image: string;
  imageUrl: string; // Add imageUrl for admin compatibility
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  year: number;
  status: 'completed' | 'in-progress' | 'planned';
  createdAt?: string;
  updatedAt?: string;
  // GitHub-specific fields
  githubData?: {
    id: number;
    name: string;
    fullName: string;
    stars: number;
    forks: number;
    language: string | null;
    topics: string[];
    lastUpdated: string;
    createdAt: string;
    size: number;
    openIssues: number;
    hasPages: boolean;
    homepage: string | null;
    archived: boolean;
    disabled: boolean;
    private: boolean;
  };
  syncedFromGithub?: boolean;
  githubSyncedAt?: string;
}

export interface ProjectForm {
  title: string;
  description: string;
  longDescription?: string;
  technologies: string[];
  category: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'other';
  image: string;
  imageUrl: string;
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  year: number;
  status: 'completed' | 'in-progress' | 'planned';
}

export interface Skill {
  id?: string;
  name: string;
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'languages' | 'ai' | 'machine-learning' | 'other';
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  icon?: string;
  color?: string;
}

export interface Experience {
  id?: string;
  company: string;
  position: string;
  duration: string;
  description: string[];
  technologies: string[];
  type: 'work' | 'internship' | 'freelance' | 'project';
}

export interface Education {
  id?: string;
  institution: string;
  degree: string;
  field: string;
  duration: string;
  gpa?: string;
  achievements?: string[];
}

export interface ContactForm {
  name: string;
  email: string;
  subject?: string;
  message: string;
}

export interface ContactMessage extends ContactForm {
  id: string;
  subject: string; // Make subject required for messages
  createdAt: string;
  read: boolean;
}

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'admin';
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
  color: string; // Make color required to fix type issues
}

export interface PersonalInfo {
  name: string;
  title: string;
  tagline: string;
  bio: string;
  location: string;
  email: string;
  phone?: string;
  resumeUrl: string;
  profileImage?: string;
  socialLinks: SocialLink[];
}

export interface ThemeConfig {
  isDark: boolean;
  primaryColor: string;
  accentColor: string;
}

export interface AnimationConfig {
  duration: number;
  delay: number;
  easing: string;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error handling types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

// Form types
export interface FormFieldError {
  message: string;
  type: string;
}

export interface FormState<T = Record<string, unknown>> {
  data: T;
  errors: Record<keyof T, FormFieldError | undefined>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
}



// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

export interface LoadingState {
  isLoading: boolean;
  loadingText?: string;
}

export interface AsyncState<T> extends LoadingState {
  data?: T;
  error?: string;
  lastUpdated?: string;
}

// Navigation types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  isActive?: boolean;
  isExternal?: boolean;
}

// Theme types
export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  border: string;
}

export interface ThemeColors {
  light: ColorScheme;
  dark: ColorScheme;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Event handler types
export type EventHandler<T = Event> = (event: T) => void;
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

// Storage types
export interface StorageItem<T> {
  value: T;
  timestamp: number;
  expiresAt?: number;
}

// Validation types
export interface ValidationRule<T = unknown> {
  validate: (value: T) => boolean | string;
  message: string;
}

export type ValidationSchema<T> = {
  [K in keyof T]: ValidationRule<T[K]>[];
};

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  author: string;
  siteUrl: string;
  image?: string;
}

export interface PortfolioData {
  personal?: PersonalInfo;
  projects?: Project[];
  skills?: Skill[];
  experience?: Experience[];
  education?: Education[];
  seo?: SEOConfig;
}

// GitHub API Types
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  homepage: string | null;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  size: number;
  open_issues_count: number;
  topics: string[];
  created_at: string;
  updated_at: string;
  pushed_at: string;
  has_pages: boolean;
  archived: boolean;
  disabled: boolean;
  private: boolean;
  fork: boolean;
  default_branch: string;
}

export interface GitHubUser {
  login: string;
  id: number;
  name: string | null;
  bio: string | null;
  public_repos: number;
  followers: number;
  following: number;
  created_at: string;
  updated_at: string;
}

export interface GitHubSyncConfig {
  username: string;
  accessToken?: string;
  includeForked: boolean;
  includePrivate: boolean;
  excludeRepos: string[];
  includeRepos: string[];
  autoSync: boolean;
  syncInterval: number; // in hours
  lastSyncAt?: string;
}

export interface GitHubSyncResult {
  success: boolean;
  message: string;
  syncedCount: number;
  skippedCount: number;
  errorCount: number;
  repositories: GitHubRepository[];
  errors: string[];
}

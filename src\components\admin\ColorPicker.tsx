'use client';

import { useState } from 'react';
import { ChevronDown, Palette } from 'lucide-react';

const predefinedColors = [
  // Primary Colors
  { name: 'Blue', value: '#3B82F6' },
  { name: 'Red', value: '#EF4444' },
  { name: 'Green', value: '#10B981' },
  { name: 'Yellow', value: '#F59E0B' },
  { name: 'Purple', value: '#8B5CF6' },
  { name: 'Pink', value: '#EC4899' },
  { name: 'Indigo', value: '#6366F1' },
  { name: 'Orange', value: '#F97316' },
  
  // Tech Colors
  { name: 'React Blue', value: '#61DAFB' },
  { name: 'Vue Green', value: '#4FC08D' },
  { name: 'Angular Red', value: '#DD0031' },
  { name: 'Node Green', value: '#339933' },
  { name: 'Python Blue', value: '#3776AB' },
  { name: 'JavaScript Yellow', value: '#F7DF1E' },
  { name: 'TypeScript Blue', value: '#3178C6' },
  { name: 'Java Orange', value: '#ED8B00' },
  
  // Additional Colors
  { name: 'Tea<PERSON>', value: '#14B8A6' },
  { name: 'Cyan', value: '#06B6D4' },
  { name: 'Emerald', value: '#059669' },
  { name: 'Lime', value: '#65A30D' },
  { name: 'Amber', value: '#D97706' },
  { name: 'Rose', value: '#F43F5E' },
  { name: 'Violet', value: '#7C3AED' },
  { name: 'Fuchsia', value: '#C026D3' },
  
  // Neutral Colors
  { name: 'Gray', value: '#6B7280' },
  { name: 'Slate', value: '#64748B' },
  { name: 'Zinc', value: '#71717A' },
  { name: 'Stone', value: '#78716C' },
];

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  className?: string;
}

export default function ColorPicker({ value, onChange, className = '' }: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [customColor, setCustomColor] = useState(value);

  // Find the predefined color name
  const selectedColor = predefinedColors.find(color => color.value.toLowerCase() === value.toLowerCase());

  const handleColorSelect = (colorValue: string) => {
    onChange(colorValue);
    setCustomColor(colorValue);
    setIsOpen(false);
  };

  const handleCustomColorChange = (colorValue: string) => {
    setCustomColor(colorValue);
    onChange(colorValue);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Current Color Display / Trigger */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white flex items-center justify-between"
      >
        <div className="flex items-center space-x-2">
          <div 
            className="w-4 h-4 rounded border border-gray-300 dark:border-gray-600"
            style={{ backgroundColor: value }}
          />
          <span className="text-sm">
            {selectedColor ? selectedColor.name : value}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden">
          {/* Custom Color Input */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              Custom Color
            </label>
            <div className="flex space-x-2">
              <input
                type="color"
                value={customColor}
                onChange={(e) => handleCustomColorChange(e.target.value)}
                className="w-10 h-8 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
              />
              <input
                type="text"
                value={customColor}
                onChange={(e) => handleCustomColorChange(e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="#3B82F6"
              />
            </div>
          </div>

          {/* Predefined Colors */}
          <div className="p-3">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              Predefined Colors
            </label>
            <div className="grid grid-cols-4 gap-2 max-h-48 overflow-y-auto">
              {predefinedColors.map((color) => (
                <button
                  key={color.value}
                  onClick={() => handleColorSelect(color.value)}
                  className={`flex items-center space-x-2 p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left ${
                    value.toLowerCase() === color.value.toLowerCase() 
                      ? 'bg-blue-100 dark:bg-blue-900/30 ring-2 ring-blue-500' 
                      : ''
                  }`}
                  title={color.name}
                >
                  <div 
                    className="w-4 h-4 rounded border border-gray-300 dark:border-gray-600 flex-shrink-0"
                    style={{ backgroundColor: color.value }}
                  />
                  <span className="text-xs truncate">{color.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

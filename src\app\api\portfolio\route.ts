import { NextResponse } from 'next/server';

import { getPortfolioData } from '@/lib/storage';

export async function GET() {
  try {
    const data = await getPortfolioData();

    // Sort projects by order field, with fallback to creation date
    if (data.projects) {
      data.projects = [...data.projects].sort((a, b) => {
        // If both have order, sort by order
        if (typeof a.order === 'number' && typeof b.order === 'number') {
          return a.order - b.order;
        }

        // If only one has order, prioritize it
        if (typeof a.order === 'number') return -1;
        if (typeof b.order === 'number') return 1;

        // If neither has order, sort by creation date (newest first)
        const aDate = new Date(a.createdAt || 0).getTime();
        const bDate = new Date(b.createdAt || 0).getTime();
        return bDate - aDate;
      });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}

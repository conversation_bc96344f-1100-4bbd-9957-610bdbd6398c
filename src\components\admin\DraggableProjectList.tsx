'use client';

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Edit,
  Trash2,
  ExternalLink,
  Github,
  Calendar,
  Star,
  GitFork,
  GripVertical,
  AlertCircle,
  CheckCircle,
  Undo2,
  FolderOpen,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';

import { ApiService } from '@/lib/api-service';
import type { Project } from '@/types';

interface DraggableProjectCardProps {
  project: Project;
  onEdit: (project: Project) => void;
  onDelete: (projectId: string) => void;
  isDragging?: boolean;
}

function DraggableProjectCard({ project, onEdit, onDelete, isDragging = false }: DraggableProjectCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: project.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200 ${
        isSortableDragging ? 'shadow-2xl scale-105 z-50' : 'hover:shadow-lg'
      } ${isDragging ? 'ring-2 ring-primary-500 ring-opacity-50' : ''}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg z-10 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        title="Drag to reorder project"
        aria-label={`Drag to reorder ${project.title}`}
        role="button"
        tabIndex={0}
      >
        <GripVertical className="w-4 h-4 text-gray-400 dark:text-gray-500" />
      </div>

      <div className="flex p-6 pl-12">
        <div className="relative w-24 h-24 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0">
          {(project.imageUrl || project.image) ? (
            <Image
              src={project.imageUrl || project.image || '/placeholder-project.jpg'}
              alt={project.title}
              fill
              className="object-cover"
              sizes="96px"
              onError={(e) => {
                // Fallback to placeholder if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.parentElement!.innerHTML = `
                  <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                    <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                    </svg>
                  </div>
                `;
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
              <FolderOpen className="w-8 h-8 text-gray-400 dark:text-gray-500" />
            </div>
          )}
        </div>

        <div className="ml-6 flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                  {project.title}
                </h3>
                {/* Status Badge moved to header */}
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  project.status === 'completed'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : project.status === 'in-progress'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                }`}>
                  {project.status.replace('-', ' ')}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                {project.category}
              </p>
            </div>

            <div className="flex items-start space-x-2 ml-4">
              <Link
                href={`/admin/projects/${project.id}/edit`}
                className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Edit project"
              >
                <Edit className="w-4 h-4" />
              </Link>
              <button
                onClick={() => onDelete(project.id)}
                className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                title="Delete project"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
            {project.description}
          </p>

          {/* GitHub Sync Indicator - simplified without stats */}
          {project.syncedFromGithub && (
            <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full w-fit mb-3">
              <Github className="w-3 h-3" />
              <span>GitHub Sync</span>
            </div>
          )}

          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Calendar className="w-3 h-3" />
                <span>{project.year}</span>
              </div>
              {project.featured && (
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 rounded-full text-xs font-medium">
                  Featured
                </span>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {project.githubUrl && (
                <a
                  href={project.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                  title="View on GitHub"
                >
                  <Github className="w-4 h-4" />
                </a>
              )}
              {project.liveUrl && (
                <a
                  href={project.liveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                  title="View live project"
                >
                  <ExternalLink className="w-4 h-4" />
                </a>
              )}
            </div>
          </div>

          <div className="flex flex-wrap gap-1">
            {project.technologies.slice(0, 4).map((tech) => (
              <span
                key={tech}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs"
              >
                {tech}
              </span>
            ))}
            {project.technologies.length > 4 && (
              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded text-xs">
                +{project.technologies.length - 4} more
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

interface DraggableProjectListProps {
  projects: Project[];
  onEdit: (project: Project) => void;
  onDelete: (projectId: string) => void;
  onReorder: (projectIds: string[]) => Promise<void>;
}

export default function DraggableProjectList({ 
  projects, 
  onEdit, 
  onDelete, 
  onReorder 
}: DraggableProjectListProps) {
  const [items, setItems] = useState<Project[]>(projects);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [isReordering, setIsReordering] = useState(false);
  const [reorderError, setReorderError] = useState<string | null>(null);
  const [reorderSuccess, setReorderSuccess] = useState(false);
  const [previousOrder, setPreviousOrder] = useState<Project[]>([]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setItems(projects);
  }, [projects]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id);
    setPreviousOrder([...items]);
    setReorderError(null);
    setReorderSuccess(false);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over?.id);

      const newItems = arrayMove(items, oldIndex, newIndex);
      setItems(newItems);

      // Save the new order to the backend
      setIsReordering(true);
      try {
        await onReorder(newItems.map(item => item.id));
        setReorderSuccess(true);
        setTimeout(() => setReorderSuccess(false), 3000);
      } catch (error) {
        console.error('Failed to reorder projects:', error);
        setReorderError('Failed to save new order. Please try again.');
        // Revert to previous order on error
        setItems(previousOrder);
        setTimeout(() => setReorderError(null), 5000);
      } finally {
        setIsReordering(false);
      }
    }

    setActiveId(null);
  };

  const handleUndo = () => {
    if (previousOrder.length > 0) {
      setItems(previousOrder);
      onReorder(previousOrder.map(item => item.id)).catch(console.error);
      setPreviousOrder([]);
      setReorderError(null);
    }
  };

  const activeProject = activeId ? items.find(item => item.id === activeId) : null;

  return (
    <div className="space-y-4">
      {/* Helper Text */}
      {items.length > 1 && (
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <GripVertical className="w-4 h-4 text-blue-500" />
          <span>
            Drag the grip handle on the left of each project card to reorder your projects.
            The new order will be saved automatically and reflected on your public portfolio.
          </span>
        </div>
      )}

      {/* Status Messages */}
      <AnimatePresence>
        {isReordering && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 text-sm"
          >
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 dark:border-blue-400"></div>
            <span>Saving new order...</span>
          </motion.div>
        )}

        {reorderSuccess && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-green-600 dark:text-green-400 text-sm"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Projects reordered successfully!</span>
          </motion.div>
        )}

        {reorderError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center justify-between bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3"
          >
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{reorderError}</span>
            </div>
            {previousOrder.length > 0 && (
              <button
                onClick={handleUndo}
                className="flex items-center space-x-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium"
              >
                <Undo2 className="w-3 h-3" />
                <span>Undo</span>
              </button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items.map(item => item.id)} strategy={verticalListSortingStrategy}>
          <div className="space-y-4">
            {items.map((project) => (
              <DraggableProjectCard
                key={project.id}
                project={project}
                onEdit={onEdit}
                onDelete={onDelete}
                isDragging={activeId === project.id}
              />
            ))}
          </div>
        </SortableContext>

        <DragOverlay>
          {activeProject ? (
            <DraggableProjectCard
              project={activeProject}
              onEdit={onEdit}
              onDelete={onDelete}
              isDragging={true}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {items.length === 0 && (
        <div className="text-center py-12 text-gray-500 dark:text-gray-400">
          <p>No projects found. Create your first project to get started!</p>
        </div>
      )}
    </div>
  );
}

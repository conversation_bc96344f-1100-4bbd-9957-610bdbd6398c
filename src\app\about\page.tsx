'use client';

import { motion } from 'framer-motion';
import { Download, MapPin, Calendar, Award, BookOpen } from 'lucide-react';
import React, { useState, useEffect } from 'react';

// import { personalInfo, education, experience, skills } from '@/data/portfolio';
import SkillsGrid from '@/components/sections/SkillsGrid';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { ApiService } from '@/lib/api-service';
import type { PersonalInfo, Education, Experience } from '@/types';

interface PortfolioData {
  personal?: PersonalInfo;
  education?: Education[];
  experience?: Experience[];
}

export default function AboutPage() {
  const [portfolioData, setPortfolioData] = useState<PortfolioData>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await ApiService.getPortfolio();
        if (response.success && response.data) {
          setPortfolioData(response.data);
        } else {
          console.error('Failed to fetch portfolio data:', response.error);
          setPortfolioData({});
        }
      } catch (error) {
        console.error('Failed to fetch portfolio data:', error);
        setPortfolioData({});
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const { personal: personalInfo, education = [], experience = [] } = portfolioData;
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
            About Me
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Get to know more about my journey, skills, and what drives me as a developer
          </p>
        </motion.div>

        {loading ? (
          <div className="text-center py-20">
            <div className="animate-pulse text-gray-500 dark:text-gray-400 text-lg">
              Loading about information...
            </div>
          </div>
        ) : (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-16"
          >
            {/* Bio Section */}
            <motion.section variants={itemVariants}>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Profile Image Placeholder */}
                <div className="lg:col-span-1">
                  <Card className="text-center">
                    <div className="w-48 h-48 mx-auto bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 rounded-full flex items-center justify-center mb-6">
                      <span className="text-6xl font-bold text-primary-500 dark:text-primary-400">
                        {personalInfo?.name
                          ? personalInfo.name
                            .split(' ')
                            .map((n: string) => n[0])
                            .join('')
                          : 'YN'}
                      </span>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {personalInfo?.name || 'Your Name'}
                    </h2>
                    <p className="text-primary-500 dark:text-primary-400 font-medium mb-4">
                      {personalInfo?.title || 'Your Title'}
                    </p>
                    <div className="flex items-center justify-center text-gray-600 dark:text-gray-400 mb-6">
                      <MapPin size={16} className="mr-2" />
                      {personalInfo?.location || 'Location'}
                    </div>
                    <Button
                      icon={<Download size={20} />}
                      onClick={() =>
                        personalInfo?.resumeUrl && window.open(personalInfo.resumeUrl, '_blank')
                      }
                      fullWidth
                    >
                      Download Resume
                    </Button>
                  </Card>
                </div>

                {/* Bio Content */}
                <div className="lg:col-span-2">
                  <Card>
                    <Card.Header>
                      <Card.Title>
                        {personalInfo?.storyTitle || 'My Story'}
                      </Card.Title>
                    </Card.Header>
                    <Card.Content>
                      {/* Story Image */}
                      {personalInfo?.storyImage && (
                        <div className="mb-6">
                          <img
                            src={personalInfo.storyImage}
                            alt="My story"
                            className="w-full h-64 object-cover rounded-lg shadow-lg"
                          />
                        </div>
                      )}

                      {/* Story Content */}
                      <div className="prose prose-lg dark:prose-invert max-w-none">
                        {personalInfo?.storyContent ? (
                          <div
                            dangerouslySetInnerHTML={{
                              __html: personalInfo.storyContent
                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                                .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
                                .replace(/^> (.+)$/gm, '<blockquote class="border-l-4 border-primary-500 pl-4 italic text-gray-600 dark:text-gray-400">$1</blockquote>')
                                .replace(/^- (.+)$/gm, '<li class="text-gray-600 dark:text-gray-400">$1</li>')
                                .replace(/^1\. (.+)$/gm, '<li class="text-gray-600 dark:text-gray-400">$1</li>')
                                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-primary-600 dark:text-primary-400 hover:underline">$1</a>')
                                .replace(/^### (.+)$/gm, '<h3 class="text-xl font-semibold text-gray-900 dark:text-white mt-6 mb-3">$1</h3>')
                                .replace(/^## (.+)$/gm, '<h2 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">$1</h2>')
                                .replace(/^# (.+)$/gm, '<h1 class="text-3xl font-bold text-gray-900 dark:text-white mt-8 mb-4">$1</h1>')
                                .replace(/\n/g, '<br>')
                            }}
                            className="text-gray-600 dark:text-gray-400 leading-relaxed"
                          />
                        ) : (
                          <>
                            <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                              {personalInfo?.bio || 'Your bio will appear here.'}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                              As a recent graduate in Software Engineering from Samsun University, I
                              bring fresh perspectives and modern approaches to software development. My
                              journey in tech started with curiosity about how things work and evolved
                              into a passion for creating solutions that make a difference.
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                              I&apos;m particularly interested in full-stack development, user
                              experience design, and emerging technologies. When I&apos;m not coding,
                              you can find me exploring new frameworks, contributing to open-source
                              projects, or sharing knowledge with the developer community.
                            </p>
                          </>
                        )}
                      </div>
                    </Card.Content>
                  </Card>
                </div>
              </div>
            </motion.section>

            {/* Education Section */}
            <motion.section variants={itemVariants}>
              <h2 className="text-3xl font-bold font-display text-gray-900 dark:text-white mb-8 text-center">
                Education
              </h2>
              <div className="space-y-6">
                {education.map(edu => (
                  <Card key={edu.id} hover>
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
                        <BookOpen className="text-primary-500 dark:text-primary-400" size={24} />
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {edu.degree} in {edu.field}
                          </h3>
                          <div className="flex items-center text-gray-600 dark:text-gray-400">
                            <Calendar size={16} className="mr-2" />
                            {edu.duration}
                          </div>
                        </div>
                        <p className="text-primary-500 dark:text-primary-400 font-medium mb-2">
                          {edu.institution}
                        </p>
                        {edu.gpa && (
                          <p className="text-gray-600 dark:text-gray-400 mb-3">GPA: {edu.gpa}</p>
                        )}
                        {edu.achievements && (
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                              Key Achievements:
                            </h4>
                            <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
                              {edu.achievements.map((achievement, index) => (
                                <li key={index}>{achievement}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </motion.section>

            {/* Experience Section */}
            <motion.section variants={itemVariants}>
              <h2 className="text-3xl font-bold font-display text-gray-900 dark:text-white mb-8 text-center">
                Experience
              </h2>
              <div className="space-y-6">
                {experience.map(exp => (
                  <Card key={exp.id} hover>
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-accent-100 dark:bg-accent-900/30 rounded-lg">
                        <Award className="text-accent-500 dark:text-accent-400" size={24} />
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {exp.position}
                          </h3>
                          <div className="flex items-center text-gray-600 dark:text-gray-400">
                            <Calendar size={16} className="mr-2" />
                            {exp.duration}
                          </div>
                        </div>
                        <p className="text-accent-500 dark:text-accent-400 font-medium mb-3">
                          {exp.company}
                        </p>
                        <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-2 mb-4">
                          {exp.description.map((desc, index) => (
                            <li key={index}>{desc}</li>
                          ))}
                        </ul>
                        <div className="flex flex-wrap gap-2">
                          {exp.technologies.map(tech => (
                            <span
                              key={tech}
                              className="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </motion.section>

            {/* Skills Section */}
            <motion.section variants={itemVariants}>
              <h2 className="text-3xl font-bold font-display text-gray-900 dark:text-white mb-8 text-center">
                Skills & Technologies
              </h2>
              <SkillsGrid />
            </motion.section>
          </motion.div>
        )}
      </div>
    </div>
  );
}

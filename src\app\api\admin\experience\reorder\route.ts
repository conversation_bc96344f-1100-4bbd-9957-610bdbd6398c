import { NextRequest, NextResponse } from 'next/server';

import { getAuthenticatedUser } from '@/lib/auth-server';
import { getPortfolioData, savePortfolioData } from '@/lib/storage';
import type { Experience } from '@/types';

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { experienceIds } = await request.json();

    if (!Array.isArray(experienceIds)) {
      return NextResponse.json(
        { error: 'Experience IDs must be an array' },
        { status: 400 }
      );
    }

    // Get current portfolio data
    const portfolioData = await getPortfolioData();
    const experience = portfolioData.experience || [];

    // Validate that all provided IDs exist
    const existingIds = new Set(experience.map(e => e.id));
    const invalidIds = experienceIds.filter(id => !existingIds.has(id));
    
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid experience IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      );
    }

    // Check if all experience items are included in the reorder
    if (experienceIds.length !== experience.length) {
      return NextResponse.json(
        { error: 'All experience items must be included in the reorder' },
        { status: 400 }
      );
    }

    // Create a map of experience by ID for efficient lookup
    const experienceMap = new Map<string, Experience>();
    experience.forEach(exp => {
      experienceMap.set(exp.id!, exp);
    });

    // Reorder experience and assign new order values
    const reorderedExperience: Experience[] = experienceIds.map((id: string, index: number) => {
      const exp = experienceMap.get(id)!;
      return {
        ...exp,
        order: index,
        updatedAt: new Date().toISOString(),
      };
    });

    // Save the reordered experience
    const updatedPortfolioData = {
      ...portfolioData,
      experience: reorderedExperience,
    };

    const saveResult = await savePortfolioData(updatedPortfolioData);

    if (!saveResult.success) {
      return NextResponse.json(
        { error: 'Failed to save experience order' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Experience reordered successfully',
      experience: reorderedExperience,
    });
  } catch (error) {
    console.error('Error reordering experience:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve experience in their current order
export async function GET() {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const portfolioData = await getPortfolioData();
    const experience = portfolioData.experience || [];

    // Sort experience by order field, with fallback to creation date
    const sortedExperience = [...experience].sort((a, b) => {
      // If both have order, sort by order
      if (typeof a.order === 'number' && typeof b.order === 'number') {
        return a.order - b.order;
      }
      
      // If only one has order, prioritize it
      if (typeof a.order === 'number') return -1;
      if (typeof b.order === 'number') return 1;
      
      // If neither has order, sort by creation date (newest first)
      const aDate = new Date(a.createdAt || 0).getTime();
      const bDate = new Date(b.createdAt || 0).getTime();
      return bDate - aDate;
    });

    return NextResponse.json({
      success: true,
      experience: sortedExperience,
    });
  } catch (error) {
    console.error('Error fetching ordered experience:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

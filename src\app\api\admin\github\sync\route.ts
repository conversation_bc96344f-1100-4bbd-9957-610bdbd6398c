import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

import { getAuthenticatedUser } from '@/lib/auth-server';
import { GitHubService } from '@/lib/github-service';
import { getPortfolioData, savePortfolioData } from '@/lib/storage';
import type { GitHubSyncResult, Project } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { username, accessToken, includeForked = false, includePrivate = false, selectedRepos = [] } = body;

    if (!username) {
      return NextResponse.json({ error: 'GitHub username is required' }, { status: 400 });
    }

    if (!GitHubService.validateUsername(username)) {
      return NextResponse.json({ error: 'Invalid GitHub username format' }, { status: 400 });
    }

    if (accessToken && !GitHubService.validateAccessToken(accessToken)) {
      return NextResponse.json({ error: 'Invalid GitHub access token format' }, { status: 400 });
    }

    // Fetch repositories from GitHub
    const repositories = await GitHubService.fetchUserRepositories(username, accessToken, {
      includeForked,
      includePrivate,
    });

    // Filter repositories if specific repos are selected
    const filteredRepos = selectedRepos.length > 0 
      ? repositories.filter(repo => selectedRepos.includes(repo.name))
      : repositories;

    // Get current portfolio data
    const portfolioData = await getPortfolioData();
    const existingProjects = portfolioData.projects || [];

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];
    const newProjects: Project[] = [];

    for (const repo of filteredRepos) {
      try {
        // Check if project already exists (by GitHub repo ID or URL)
        const existingProject = existingProjects.find(p => 
          p.githubData?.id === repo.id || 
          p.githubUrl === repo.html_url
        );

        if (existingProject) {
          // Update existing project with latest GitHub data
          const updatedProject: Project = {
            ...existingProject,
            // Update GitHub-specific data
            githubData: {
              id: repo.id,
              name: repo.name,
              fullName: repo.full_name,
              stars: repo.stargazers_count,
              forks: repo.forks_count,
              language: repo.language,
              topics: repo.topics,
              lastUpdated: repo.updated_at,
              createdAt: repo.created_at,
              size: repo.size,
              openIssues: repo.open_issues_count,
              hasPages: repo.has_pages,
              homepage: repo.homepage,
              archived: repo.archived,
              disabled: repo.disabled,
              private: repo.private,
            },
            githubSyncedAt: new Date().toISOString(),
            // Update live URL if it has GitHub Pages
            liveUrl: existingProject.liveUrl || repo.homepage || (repo.has_pages ? `https://${repo.full_name.split('/')[0]}.github.io/${repo.name}` : undefined),
          };

          // Replace the existing project
          const projectIndex = existingProjects.findIndex(p => p.id === existingProject.id);
          existingProjects[projectIndex] = updatedProject;
          syncedCount++;
        } else {
          // Create new project from repository
          const projectData = GitHubService.transformRepositoryToProject(repo);
          const newProject: Project = {
            ...projectData,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          newProjects.push(newProject);
          syncedCount++;
        }
      } catch (error) {
        console.error(`Error processing repository ${repo.name}:`, error);
        errors.push(`Failed to process ${repo.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        errorCount++;
      }
    }

    // Add new projects to the beginning of the array
    const updatedProjects = [...newProjects, ...existingProjects];

    // Save updated portfolio data
    const saveResult = await savePortfolioData({
      ...portfolioData,
      projects: updatedProjects,
    });

    if (!saveResult.success) {
      return NextResponse.json(
        { error: 'Failed to save synced projects' },
        { status: 500 }
      );
    }

    const result: GitHubSyncResult = {
      success: true,
      message: `Successfully synced ${syncedCount} repositories`,
      syncedCount,
      skippedCount,
      errorCount,
      repositories: filteredRepos,
      errors,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('GitHub sync error:', error);
    
    const result: GitHubSyncResult = {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      syncedCount: 0,
      skippedCount: 0,
      errorCount: 1,
      repositories: [],
      errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
    };

    return NextResponse.json(result, { status: 500 });
  }
}

// GET endpoint to fetch repositories without syncing
export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    const accessToken = searchParams.get('accessToken');
    const includeForked = searchParams.get('includeForked') === 'true';
    const includePrivate = searchParams.get('includePrivate') === 'true';

    if (!username) {
      return NextResponse.json({ error: 'GitHub username is required' }, { status: 400 });
    }

    if (!GitHubService.validateUsername(username)) {
      return NextResponse.json({ error: 'Invalid GitHub username format' }, { status: 400 });
    }

    const repositories = await GitHubService.fetchUserRepositories(username, accessToken || undefined, {
      includeForked,
      includePrivate,
    });

    return NextResponse.json({ repositories });
  } catch (error) {
    console.error('Error fetching GitHub repositories:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch repositories' },
      { status: 500 }
    );
  }
}

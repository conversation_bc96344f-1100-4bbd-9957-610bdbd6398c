'use client';

import { useState } from 'react';
import {
  // Programming & Development
  Code2, Code, Terminal, FileCode, GitBranch, Github, Database, Server, Cloud, 
  // Frontend Technologies
  Palette, Brush, Layout, Monitor, Tablet, Globe, Eye,  // Backend & Tools
  Wrench, Settings, Cog, Package, Box, Layers, Component,
  // Languages & Frameworks
  Languages, Type, Hash, Brackets, Braces, 
  // AI & Machine Learning
  Brain, Bot, Cpu, Zap, Sparkles, Target, TrendingUp, BarChart,
  // Data & Analytics
  PieChart, LineChart, BarChart3, Activity, Gauge, 
  // Design & UI
  Figma, Paintbrush, Image, Camera, Video, Music,
  // Mobile & Devices
  Smartphone, Watch, Headphones, Gamepad2,
  // Cloud & DevOps
  CloudLightning, Shield, Lock, Key, Wifi, Network,
  // General Tech
  Lightbulb, Rocket, Star, Award, Trophy, Medal,
  
  
  
  
  
  
  ChevronDown,
} from 'lucide-react';

const iconCategories = {
  'Programming': [
    { name: 'Code2', icon: Code2, value: 'code2' },
    { name: 'Code', icon: Code, value: 'code' },
    { name: 'Terminal', icon: Terminal, value: 'terminal' },
    { name: 'FileCode', icon: FileCode, value: 'file-code' },
    { name: 'GitBranch', icon: GitBranch, value: 'git-branch' },
    { name: 'Github', icon: Github, value: 'github' },
    { name: 'Database', icon: Database, value: 'database' },
    { name: 'Server', icon: Server, value: 'server' },
    { name: 'Cloud', icon: Cloud, value: 'cloud' },
  ],
  'Frontend': [
    { name: 'Palette', icon: Palette, value: 'palette' },
    { name: 'Brush', icon: Brush, value: 'brush' },
    { name: 'Layout', icon: Layout, value: 'layout' },
    { name: 'Monitor', icon: Monitor, value: 'monitor' },
    { name: 'Smartphone', icon: Smartphone, value: 'smartphone' },
    { name: 'Tablet', icon: Tablet, value: 'tablet' },
    { name: 'Globe', icon: Globe, value: 'globe' },
    { name: 'Eye', icon: Eye, value: 'eye' },
  ],
  'Tools': [
    { name: 'Wrench', icon: Wrench, value: 'wrench' },
    { name: 'Settings', icon: Settings, value: 'settings' },
    { name: 'Cog', icon: Cog, value: 'cog' },
    { name: 'Package', icon: Package, value: 'package' },
    { name: 'Box', icon: Box, value: 'box' },
    { name: 'Layers', icon: Layers, value: 'layers' },
    { name: 'Component', icon: Component, value: 'component' },
  ],
  'Languages': [
    { name: 'Languages', icon: Languages, value: 'languages' },
    { name: 'Type', icon: Type, value: 'type' },
    { name: 'Hash', icon: Hash, value: 'hash' },
    { name: 'Brackets', icon: Brackets, value: 'brackets' },
    { name: 'Braces', icon: Braces, value: 'braces' },
  ],
  'AI & ML': [
    { name: 'Brain', icon: Brain, value: 'brain' },
    { name: 'Bot', icon: Bot, value: 'bot' },
    { name: 'Cpu', icon: Cpu, value: 'cpu' },
    { name: 'Zap', icon: Zap, value: 'zap' },
    { name: 'Sparkles', icon: Sparkles, value: 'sparkles' },
    { name: 'Target', icon: Target, value: 'target' },
    { name: 'TrendingUp', icon: TrendingUp, value: 'trending-up' },
  ],
  'Data': [
    { name: 'BarChart', icon: BarChart, value: 'bar-chart' },
    { name: 'PieChart', icon: PieChart, value: 'pie-chart' },
    { name: 'LineChart', icon: LineChart, value: 'line-chart' },
    { name: 'BarChart3', icon: BarChart3, value: 'bar-chart-3' },
    { name: 'Activity', icon: Activity, value: 'activity' },
    { name: 'Gauge', icon: Gauge, value: 'gauge' },
  ],
  'Design': [
    { name: 'Figma', icon: Figma, value: 'figma' },
    { name: 'Paintbrush', icon: Paintbrush, value: 'paintbrush' },
    { name: 'Image', icon: Image, value: 'image' },
    { name: 'Camera', icon: Camera, value: 'camera' },
    { name: 'Video', icon: Video, value: 'video' },
    { name: 'Music', icon: Music, value: 'music' },
  ],
  'Mobile': [
    { name: 'Smartphone', icon: Smartphone, value: 'smartphone' },
    { name: 'Watch', icon: Watch, value: 'watch' },
    { name: 'Headphones', icon: Headphones, value: 'headphones' },
    { name: 'Gamepad2', icon: Gamepad2, value: 'gamepad-2' },
  ],
  'Cloud': [
    { name: 'CloudLightning', icon: CloudLightning, value: 'cloud-lightning' },
    { name: 'Shield', icon: Shield, value: 'shield' },
    { name: 'Lock', icon: Lock, value: 'lock' },
    { name: 'Key', icon: Key, value: 'key' },
    { name: 'Wifi', icon: Wifi, value: 'wifi' },
    { name: 'Network', icon: Network, value: 'network' },
  ],
  'General': [
    { name: 'Lightbulb', icon: Lightbulb, value: 'lightbulb' },
    { name: 'Rocket', icon: Rocket, value: 'rocket' },
    { name: 'Star', icon: Star, value: 'star' },
    { name: 'Award', icon: Award, value: 'award' },
    { name: 'Trophy', icon: Trophy, value: 'trophy' },
    { name: 'Medal', icon: Medal, value: 'medal' },
  ],
};

interface IconPickerProps {
  value: string;
  onChange: (iconValue: string) => void;
  className?: string;
}

export default function IconPicker({ value, onChange, className = '' }: IconPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('Programming');
  const [searchTerm, setSearchTerm] = useState('');

  // Find the current icon
  const currentIcon = Object.values(iconCategories)
    .flat()
    .find(icon => icon.value === value);

  // Filter icons based on search term
  const filteredIcons = searchTerm
    ? Object.values(iconCategories)
        .flat()
        .filter(icon => 
          icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          icon.value.toLowerCase().includes(searchTerm.toLowerCase())
        )
    : iconCategories[selectedCategory as keyof typeof iconCategories] || [];

  const handleIconSelect = (iconValue: string) => {
    onChange(iconValue);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Current Icon Display / Trigger */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white flex items-center justify-between"
      >
        <div className="flex items-center space-x-2">
          {currentIcon ? (
            <>
              <currentIcon.icon className="w-4 h-4" />
              <span className="text-sm">{currentIcon.name}</span>
            </>
          ) : (
            <span className="text-sm text-gray-500">Select an icon</span>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden">
          {/* Search */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <input
              type="text"
              placeholder="Search icons..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          {/* Categories */}
          {!searchTerm && (
            <div className="flex overflow-x-auto border-b border-gray-200 dark:border-gray-700">
              {Object.keys(iconCategories).map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-3 py-2 text-xs font-medium whitespace-nowrap border-b-2 transition-colors ${
                    selectedCategory === category
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          )}

          {/* Icons Grid */}
          <div className="p-2 max-h-48 overflow-y-auto">
            <div className="grid grid-cols-6 gap-1">
              {filteredIcons.map((icon) => (
                <button
                  key={icon.value}
                  onClick={() => handleIconSelect(icon.value)}
                  className={`p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    value === icon.value ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : ''
                  }`}
                  title={icon.name}
                >
                  <icon.icon className="w-5 h-5 mx-auto" />
                </button>
              ))}
            </div>
            {filteredIcons.length === 0 && (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
                No icons found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

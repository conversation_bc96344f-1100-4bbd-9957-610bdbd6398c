{"personal": {"name": "<PERSON><PERSON><PERSON>", "title": "Software Engineer", "tagline": "Crafting scalable, elegant solutions with code", "bio": "Passionate software engineer with a fresh perspective on modern development. I love building user-centric applications that solve real-world problems through clean, efficient code and thoughtful design.", "location": "Samsun, Turkey", "email": "<EMAIL>", "resumeUrl": "/resume.pdf", "socialLinks": [{"name": "GitHub", "url": "https://github.com/EhsanAmini770", "icon": "github", "color": "#333"}, {"name": "LinkedIn", "url": "https://www.linkedin.com/in/ehsan-amini-895a62362/", "icon": "linkedin", "color": "#0077B5"}, {"name": "Email", "url": "<EMAIL>", "icon": "mail", "color": "#EA4335"}]}, "skills": [{"id": "1752565906844", "name": "React", "category": "frontend", "level": "advanced", "icon": "code", "color": "#3B82F6"}, {"id": "1752565948910", "name": "java", "category": "backend", "level": "advanced", "icon": "code", "color": "#7c3838"}, {"id": "1752576145454", "name": "nextjs", "category": "frontend", "level": "intermediate", "icon": "code", "color": "#3B82F6"}, {"id": "1752618712600", "name": "python", "category": "tools", "level": "expert", "icon": "code", "color": "#f73b3b"}], "projects": [{"title": "Big_data_CICIDS2018", "description": "A Jupyter Notebook project", "technologies": ["Jupyter Notebook"], "category": "other", "image": "/api/github/repo-image/EhsanAmini770/big_data_CICIDS2018", "imageUrl": "/api/github/repo-image/EhsanAmini770/big_data_CICIDS2018", "githubUrl": "https://github.com/EhsanAmini770/big_data_CICIDS2018", "featured": false, "year": 2025, "status": "in-progress", "githubData": {"id": 1006027802, "name": "big_data_CICIDS2018", "fullName": "EhsanAmini770/big_data_CICIDS2018", "stars": 0, "forks": 0, "language": "Jupyter Notebook", "topics": [], "lastUpdated": "2025-06-21T10:27:05Z", "createdAt": "2025-06-21T10:26:06Z", "size": 16677, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "17530061205635bwjoqfnl", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "Charity_info", "description": "A TypeScript project", "technologies": ["TypeScript"], "category": "frontend", "image": "/api/github/repo-image/EhsanAmini770/charity_info", "imageUrl": "/api/github/repo-image/EhsanAmini770/charity_info", "githubUrl": "https://github.com/EhsanAmini770/charity_info", "liveUrl": "https://charity-info.vercel.app", "featured": false, "year": 2025, "status": "in-progress", "githubData": {"id": 1006021083, "name": "charity_info", "fullName": "EhsanAmini770/charity_info", "stars": 0, "forks": 0, "language": "TypeScript", "topics": [], "lastUpdated": "2025-06-21T10:14:51Z", "createdAt": "2025-06-21T10:06:41Z", "size": 111354, "openIssues": 0, "hasPages": false, "homepage": "https://charity-info.vercel.app", "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563jtdmtpz84", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "Photo2Sketch DeepLearning", "description": "A Python project", "technologies": ["Python"], "category": "backend", "image": "/api/github/repo-image/EhsanAmini770/Photo2Sketch-DeepLearning", "imageUrl": "/api/github/repo-image/EhsanAmini770/Photo2Sketch-DeepLearning", "githubUrl": "https://github.com/EhsanAmini770/Photo2Sketch-DeepLearning", "featured": false, "year": 2025, "status": "in-progress", "githubData": {"id": 1006013350, "name": "Photo2Sketch-DeepLearning", "fullName": "EhsanAmini770/Photo2Sketch-DeepLearning", "stars": 0, "forks": 0, "language": "Python", "topics": [], "lastUpdated": "2025-06-21T09:54:50Z", "createdAt": "2025-06-21T09:44:17Z", "size": 2310, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563gkujswvkx", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "Bitcoin Price Prediction", "description": "A Python project", "technologies": ["Python"], "category": "backend", "image": "/api/github/repo-image/EhsanAmini770/Bitcoin-Price-Prediction", "imageUrl": "/api/github/repo-image/EhsanAmini770/Bitcoin-Price-Prediction", "githubUrl": "https://github.com/EhsanAmini770/Bitcoin-Price-Prediction", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 810930028, "name": "Bitcoin-Price-Prediction", "fullName": "EhsanAmini770/Bitcoin-Price-Prediction", "stars": 1, "forks": 0, "language": "Python", "topics": [], "lastUpdated": "2025-04-08T17:59:35Z", "createdAt": "2024-06-05T16:07:08Z", "size": 9754, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563cad6jzvsw", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "Fuzzy Logic Membership Functions", "description": "A Jupyter Notebook project", "technologies": ["Jupyter Notebook"], "category": "other", "image": "/api/github/repo-image/EhsanAmini770/fuzzy-logic-membership-functions", "imageUrl": "/api/github/repo-image/EhsanAmini770/fuzzy-logic-membership-functions", "githubUrl": "https://github.com/EhsanAmini770/fuzzy-logic-membership-functions", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 884456552, "name": "fuzzy-logic-membership-functions", "fullName": "EhsanAmini770/fuzzy-logic-membership-functions", "stars": 0, "forks": 0, "language": "Jupyter Notebook", "topics": [], "lastUpdated": "2024-12-02T20:14:08Z", "createdAt": "2024-11-06T19:33:23Z", "size": 393, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563jhom02wxh", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "AI Regression And Neural Networks", "description": "A Jupyter Notebook project", "technologies": ["Jupyter Notebook"], "category": "other", "image": "/api/github/repo-image/EhsanAmini770/AI-Regression-and-Neural-Networks", "imageUrl": "/api/github/repo-image/EhsanAmini770/AI-Regression-and-Neural-Networks", "githubUrl": "https://github.com/EhsanAmini770/AI-Regression-and-Neural-Networks", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 802055183, "name": "AI-Regression-and-Neural-Networks", "fullName": "EhsanAmini770/AI-Regression-and-Neural-Networks", "stars": 0, "forks": 0, "language": "Jupyter Notebook", "topics": [], "lastUpdated": "2024-06-25T12:50:49Z", "createdAt": "2024-05-17T12:39:03Z", "size": 171, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563hvqwplnkt", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "CoolCulina", "description": "A JavaScript project", "technologies": ["JavaScript"], "category": "frontend", "image": "/api/github/repo-image/EhsanAmini770/CoolCulina", "imageUrl": "/api/github/repo-image/EhsanAmini770/CoolCulina", "githubUrl": "https://github.com/EhsanAmini770/CoolCulina", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 770879114, "name": "CoolCulina", "fullName": "EhsanAmini770/CoolCulina", "stars": 1, "forks": 0, "language": "JavaScript", "topics": [], "lastUpdated": "2024-06-06T17:09:15Z", "createdAt": "2024-03-12T10:20:26Z", "size": 1924, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "17530061205633piey0rrs", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "Co_Adviser", "description": "A Python project", "technologies": ["Python"], "category": "backend", "image": "/api/github/repo-image/EhsanAmini770/Co_Adviser", "imageUrl": "/api/github/repo-image/EhsanAmini770/Co_Adviser", "githubUrl": "https://github.com/EhsanAmini770/Co_Adviser", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 799836272, "name": "Co_Adviser", "fullName": "EhsanAmini770/Co_Adviser", "stars": 0, "forks": 0, "language": "Python", "topics": [], "lastUpdated": "2024-06-03T10:33:45Z", "createdAt": "2024-05-13T07:39:54Z", "size": 18343, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563i5omvy1ws", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T10:08:40.563Z"}, {"title": "Flutter_projects", "description": "A Dart project", "technologies": ["Dart"], "category": "mobile", "image": "/api/github/repo-image/EhsanAmini770/flutter_projects", "imageUrl": "/api/github/repo-image/EhsanAmini770/flutter_projects", "githubUrl": "https://github.com/EhsanAmini770/flutter_projects", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 793610836, "name": "flutter_projects", "fullName": "EhsanAmini770/flutter_projects", "stars": 0, "forks": 0, "language": "Dart", "topics": [], "lastUpdated": "2024-06-03T10:30:00Z", "createdAt": "2024-04-29T14:46:43Z", "size": 20212, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.564Z", "id": "1753006120564thwwacy5d", "createdAt": "2025-07-20T10:08:40.564Z", "updatedAt": "2025-07-20T10:08:40.564Z"}], "education": [], "experience": [], "seo": {"title": "<PERSON><PERSON><PERSON> - Software Engineer <PERSON><PERSON><PERSON>", "description": "Software Engineer specializing in modern web development. Crafting scalable, elegant solutions with React, Node.js, and Python.", "keywords": ["Software Engineer", "Web Developer", "React", "Node.js", "Python", "Full Stack Developer", "<PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON>", "siteUrl": "https://ehsan-amini.vercel.app", "image": "/og-image.jpg"}}
{"personal": {"name": "<PERSON><PERSON><PERSON>", "title": "Software Engineer", "tagline": "Crafting scalable, elegant solutions with code", "bio": "Passionate software engineer with a fresh perspective on modern development. I love building user-centric applications that solve real-world problems through clean, efficient code and thoughtful design.", "location": "Samsun, Turkey", "email": "<EMAIL>", "resumeUrl": "/resume.pdf", "socialLinks": [{"name": "GitHub", "url": "https://github.com/EhsanAmini770", "icon": "github", "color": "#333"}, {"name": "LinkedIn", "url": "https://www.linkedin.com/in/ehsan-amini-895a62362/", "icon": "linkedin", "color": "#0077B5"}, {"name": "Email", "url": "<EMAIL>", "icon": "mail", "color": "#EA4335"}]}, "skills": [{"id": "1753007342839", "name": "JavaScript", "category": "languages", "level": "advanced", "icon": "languages", "color": "#F59E0B"}, {"id": "1753007639708", "name": "Python", "category": "languages", "level": "advanced", "icon": "languages", "color": "#F59E0B"}, {"id": "1753007844851", "name": "Git & GitHub", "category": "tools", "level": "advanced", "icon": "github", "color": "#10B981"}, {"id": "1753008129132", "name": "Machine Learning", "category": "ai", "level": "advanced", "icon": "bot", "color": "#EF4444"}, {"id": "1753008788216", "name": "C#", "category": "languages", "level": "advanced", "icon": "languages", "color": "#F59E0B"}, {"id": "1753010903823", "name": "mongodb", "category": "database", "level": "advanced", "icon": "pie-chart", "color": "#3B82F6"}, {"id": "1753010925020", "name": "SQL", "category": "database", "level": "advanced", "icon": "pie-chart", "color": "#3B82F6"}, {"id": "1753011448342", "name": "MySQL", "category": "database", "level": "advanced", "icon": "code2", "color": "#3B82F6"}, {"id": "1753011493332", "name": "Firebase", "category": "database", "level": "advanced", "icon": "pie-chart", "color": "#3B82F6"}, {"id": "1753011654821", "name": "VS Code\t", "category": "tools", "level": "intermediate", "icon": "layers", "color": "#10B981"}, {"id": "1753011710094", "name": "Deep Neural Network", "category": "ai", "level": "advanced", "icon": "bot", "color": "#EF4444"}], "projects": [{"title": "Photo2Sketch DeepLearning", "description": "A Python project", "technologies": ["Python"], "category": "backend", "image": "/api/github/repo-image/EhsanAmini770/Photo2Sketch-DeepLearning", "imageUrl": "https://ars.els-cdn.com/content/image/1-s2.0-S0925231221000710-gr2.jpg", "githubUrl": "https://github.com/EhsanAmini770/Photo2Sketch-DeepLearning", "featured": true, "year": 2025, "status": "completed", "githubData": {"id": 1006013350, "name": "Photo2Sketch-DeepLearning", "fullName": "EhsanAmini770/Photo2Sketch-DeepLearning", "stars": 0, "forks": 0, "language": "Python", "topics": [], "lastUpdated": "2025-06-21T09:54:50Z", "createdAt": "2025-06-21T09:44:17Z", "size": 2310, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563gkujswvkx", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T11:16:33.653Z", "order": 0, "longDescription": "", "liveUrl": ""}, {"title": "Big_data_CICIDS2018", "description": "A Jupyter Notebook project", "technologies": ["Python"], "category": "other", "image": "https://www.unb.ca/cic/_assets/images/cse-cic-ids2018.jpg", "imageUrl": "https://www.unb.ca/cic/_assets/images/cse-cic-ids2018.jpg", "githubUrl": "https://github.com/EhsanAmini770/big_data_CICIDS2018", "featured": true, "year": 2025, "status": "completed", "githubData": {"id": 1006027802, "name": "big_data_CICIDS2018", "fullName": "EhsanAmini770/big_data_CICIDS2018", "stars": 0, "forks": 0, "language": "Jupyter Notebook", "topics": [], "lastUpdated": "2025-06-21T10:27:05Z", "createdAt": "2025-06-21T10:26:06Z", "size": 16677, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "17530061205635bwjoqfnl", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T11:33:16.791Z", "order": 1, "longDescription": "", "liveUrl": ""}, {"title": "Fuzzy Logic Membership Functions", "description": "A Jupyter Notebook project", "technologies": ["Jupyter Notebook"], "category": "other", "image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQy-tceQiBk0JsxdbwpA28L5yFvzwv9GOFFEw&s", "imageUrl": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQy-tceQiBk0JsxdbwpA28L5yFvzwv9GOFFEw&s", "githubUrl": "https://github.com/EhsanAmini770/fuzzy-logic-membership-functions", "featured": true, "year": 2024, "status": "completed", "githubData": {"id": 884456552, "name": "fuzzy-logic-membership-functions", "fullName": "EhsanAmini770/fuzzy-logic-membership-functions", "stars": 0, "forks": 0, "language": "Jupyter Notebook", "topics": [], "lastUpdated": "2024-12-02T20:14:08Z", "createdAt": "2024-11-06T19:33:23Z", "size": 393, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563jhom02wxh", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T11:16:33.653Z", "order": 2, "longDescription": "", "liveUrl": ""}, {"title": "Charity_info", "description": "A TypeScript project", "technologies": ["Node.js", "React"], "category": "frontend", "image": "https://www.nevadatrust.com/wp-content/uploads/2025/02/Depositphotos_120785370_XL-1-scaled.jpg", "imageUrl": "https://www.nevadatrust.com/wp-content/uploads/2025/02/Depositphotos_120785370_XL-1-scaled.jpg", "githubUrl": "https://github.com/EhsanAmini770/charity_info", "liveUrl": "https://charity-info.vercel.app", "featured": true, "year": 2025, "status": "in-progress", "githubData": {"id": 1006021083, "name": "charity_info", "fullName": "EhsanAmini770/charity_info", "stars": 0, "forks": 0, "language": "TypeScript", "topics": [], "lastUpdated": "2025-06-21T10:14:51Z", "createdAt": "2025-06-21T10:06:41Z", "size": 111354, "openIssues": 0, "hasPages": false, "homepage": "https://charity-info.vercel.app", "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563jtdmtpz84", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T11:16:33.653Z", "order": 3, "longDescription": ""}, {"title": "Bitcoin Price Prediction", "description": "A Python project", "technologies": ["Python"], "category": "backend", "image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRExYxzyLPvZICpw3iTO9D6v1NJj7RfAmPPqg&s", "imageUrl": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRExYxzyLPvZICpw3iTO9D6v1NJj7RfAmPPqg&s", "githubUrl": "https://github.com/EhsanAmini770/Bitcoin-Price-Prediction", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 810930028, "name": "Bitcoin-Price-Prediction", "fullName": "EhsanAmini770/Bitcoin-Price-Prediction", "stars": 1, "forks": 0, "language": "Python", "topics": [], "lastUpdated": "2025-04-08T17:59:35Z", "createdAt": "2024-06-05T16:07:08Z", "size": 9754, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563cad6jzvsw", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T11:16:33.653Z", "order": 4, "longDescription": "", "liveUrl": ""}, {"title": "Flutter_projects", "description": "A Dart project", "technologies": ["Dart"], "category": "mobile", "image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRlM9j0iuSIskKdqwu4exxAWkujmqbeiX9JWA&s", "imageUrl": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRlM9j0iuSIskKdqwu4exxAWkujmqbeiX9JWA&s", "githubUrl": "https://github.com/EhsanAmini770/flutter_projects", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 793610836, "name": "flutter_projects", "fullName": "EhsanAmini770/flutter_projects", "stars": 0, "forks": 0, "language": "Dart", "topics": [], "lastUpdated": "2024-06-03T10:30:00Z", "createdAt": "2024-04-29T14:46:43Z", "size": 20212, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.564Z", "id": "1753006120564thwwacy5d", "createdAt": "2025-07-20T10:08:40.564Z", "updatedAt": "2025-07-20T11:16:33.653Z", "order": 5, "longDescription": "", "liveUrl": ""}, {"title": "Co_Adviser", "description": "A Python project", "technologies": ["Python"], "category": "backend", "image": "/api/github/repo-image/EhsanAmini770/Co_Adviser", "imageUrl": "/api/github/repo-image/EhsanAmini770/Co_Adviser", "githubUrl": "https://github.com/EhsanAmini770/Co_Adviser", "featured": false, "year": 2024, "status": "completed", "githubData": {"id": 799836272, "name": "Co_Adviser", "fullName": "EhsanAmini770/Co_Adviser", "stars": 0, "forks": 0, "language": "Python", "topics": [], "lastUpdated": "2024-06-03T10:33:45Z", "createdAt": "2024-05-13T07:39:54Z", "size": 18343, "openIssues": 0, "hasPages": false, "homepage": null, "archived": false, "disabled": false, "private": false}, "syncedFromGithub": true, "githubSyncedAt": "2025-07-20T10:08:40.563Z", "id": "1753006120563i5omvy1ws", "createdAt": "2025-07-20T10:08:40.563Z", "updatedAt": "2025-07-20T11:16:33.653Z", "order": 6}], "education": [{"id": "1753006909758", "institution": "Samsun University", "degree": "Bachelor's Degree", "field": "Software Engineering", "duration": "2021-2025", "gpa": "", "achievements": ["Graduated with honors as an international student on a full scholarship from a competitive academic program."]}, {"id": "1753006995229", "institution": "Afghan Turk Boys High School", "degree": "High School Diploma", "field": "General Science", "duration": "2017-2020", "gpa": "", "achievements": ["Ranked among the top 1% of the School."]}], "experience": [{"id": "1753008329824", "company": "Advanced Idea Mechanics (AIM)", "position": "Flutter Developer", "duration": "1 month", "description": ["Participated in a remote internship focused on mobile application development using Flutter .", "Assisted in designing and implementing UI components and application features. ", "Collaborated with the development team via online tools to troubleshoot and improve app performance.", " Gained practical experience in cross-platform mobile development and agile workflows."], "technologies": ["Flutter"], "type": "internship"}, {"id": "1753010243119", "company": "Samsun University", "position": "Machine Learning Engineer (Final Year Project)", "duration": "Jan 2025 – Jul 2025", "description": ["Designed and developed a Real-Time Intrusion Detection System (IDS) using machine learning techniques to detect and classify live network threats.", "Utilized the CIC-IDS-2018 dataset to train and evaluate models for identifying cybersecurity attacks such as DDoS, DoS, Brute-force, and Botnet.", "Implemented both supervised learning (Random Forest) and deep learning (LSTM) models to handle complex traffic patterns and improve detection accuracy.", "Built a real-time traffic analysis pipeline with Python, incorporating data preprocessing, feature engineering, and live prediction.", "Focused on extracting security-relevant features from network traffic to enhance threat classification.", "Gained hands-on experience in network security, cyber threat analysis, and machine learning through end-to-end system development."], "technologies": ["Python", "Scikit-learn", "TensorFlow", "<PERSON><PERSON>", "LSTM", "Random Forest"], "type": "project"}], "seo": {"title": "<PERSON><PERSON><PERSON> - Software Engineer <PERSON><PERSON><PERSON>", "description": "Software Engineer specializing in modern web development. Crafting scalable, elegant solutions with React, Node.js, and Python.", "keywords": ["Software Engineer", "Web Developer", "React", "Node.js", "Python", "Full Stack Developer", "<PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON>", "siteUrl": "https://ehsan-amini.vercel.app", "image": "/og-image.jpg"}}
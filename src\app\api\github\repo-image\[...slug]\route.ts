import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string[] }> }
) {
  try {
    const { slug } = await params;
    const repoPath = slug.join('/');
    
    // For now, we'll return a placeholder image or redirect to GitHub's social preview
    // In the future, you could generate custom images or use GitHub's social preview API
    
    // Try to get GitHub's social preview image
    const githubImageUrl = `https://opengraph.githubassets.com/1/${repoPath}`;
    
    try {
      const response = await fetch(githubImageUrl);
      if (response.ok) {
        const imageBuffer = await response.arrayBuffer();
        return new NextResponse(imageBuffer, {
          headers: {
            'Content-Type': 'image/png',
            'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
          },
        });
      }
    } catch (error) {
      console.warn('Failed to fetch GitHub social preview:', error);
    }
    
    // Fallback: redirect to a placeholder image service
    const placeholderUrl = `https://via.placeholder.com/600x300/2CD3C5/ffffff?text=${encodeURIComponent(repoPath.split('/')[1] || 'Project')}`;
    
    return NextResponse.redirect(placeholderUrl);
  } catch (error) {
    console.error('Error serving repository image:', error);
    
    // Final fallback: redirect to a generic placeholder
    const fallbackUrl = 'https://via.placeholder.com/600x300/2CD3C5/ffffff?text=Project';
    return NextResponse.redirect(fallbackUrl);
  }
}

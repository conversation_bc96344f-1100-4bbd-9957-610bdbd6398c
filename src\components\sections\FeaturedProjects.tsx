'use client';

import { motion } from 'framer-motion';
import { ExternalLink, Github, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState, useEffect } from 'react';

// import { projects } from '@/data/portfolio';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { ApiService } from '@/lib/api-service';

export default function FeaturedProjects() {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await ApiService.getPortfolio();
        if (response.success && response.data) {
          setProjects(response.data.projects || []);
        } else {
          console.error('Failed to fetch projects:', response.error);
          setProjects([]);
        }
      } catch (error) {
        console.error('Failed to fetch projects:', error);
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const featuredProjects = projects.filter(project => project.featured);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section id="featured-projects" className="py-20 bg-white dark:bg-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            A showcase of my recent work and the projects I'm most proud of
          </p>
        </motion.div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-pulse text-gray-500 dark:text-gray-400">
              Loading projects...
            </div>
          </div>
        ) : featuredProjects.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400 text-lg mb-4">
              No featured projects yet. Add your projects through the admin panel.
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500">
              Mark projects as "featured" to display them here.
            </p>
          </div>
        ) : (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
          >
            {featuredProjects.map((project, index) => (
              <motion.div key={project.id} variants={itemVariants}>
                <Card hover className="h-full overflow-hidden group flex flex-col" padding="none">
                  {/* Project Image */}
                  <div className="relative h-48 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 overflow-hidden">
                    {(project.imageUrl || project.image) ? (
                      <Image
                        src={project.imageUrl || project.image}
                        alt={project.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-6xl font-bold text-primary-200 dark:text-primary-800 opacity-50">
                          {project.title.charAt(0)}
                        </div>
                      </div>
                    )}

                    {/* Overlay on hover */}
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                      {project.githubUrl && (
                        <motion.a
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                          aria-label="View source code"
                        >
                          <Github size={20} />
                        </motion.a>
                      )}

                      {project.liveUrl && (
                        <motion.a
                          href={project.liveUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                          aria-label="View live demo"
                        >
                          <ExternalLink size={20} />
                        </motion.a>
                      )}
                    </div>
                  </div>

                  <div className="p-6 flex flex-col flex-1">
                    <Card.Header className="flex-none">
                      <div className="flex items-start justify-between mb-3">
                        <Card.Title className="group-hover:text-primary-500 transition-colors flex-1 mr-3">
                          {project.title}
                        </Card.Title>
                        <div className="flex items-center space-x-2 flex-none">
                          <span className="text-sm text-text-secondary-light dark:text-text-secondary-dark bg-surface-light dark:bg-surface-dark px-2 py-1 rounded-full">
                            {project.year}
                          </span>
                          {/* Status Badge moved to header */}
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            project.status === 'completed'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : project.status === 'in-progress'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                                : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                          }`}>
                            {project.status.replace('-', ' ')}
                          </span>
                        </div>
                      </div>
                      <Card.Description className="mb-4">{project.description}</Card.Description>
                    </Card.Header>

                    <Card.Content className="flex-1">
                      {/* Technologies */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.technologies.slice(0, 4).map(tech => (
                          <span
                            key={tech}
                            className="px-3 py-1 text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                        {project.technologies.length > 4 && (
                          <span className="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
                            +{project.technologies.length - 4} more
                          </span>
                        )}
                      </div>
                    </Card.Content>

                    <Card.Footer className="flex-none mt-auto">
                      <div className="flex space-x-3">
                        {project.githubUrl && (
                          <Button
                            variant="outline"
                            size="sm"
                            icon={<Github size={16} />}
                            onClick={() => window.open(project.githubUrl, '_blank')}
                            className="flex-1"
                          >
                            Code
                          </Button>
                        )}

                        {project.liveUrl && (
                          <Button
                            size="sm"
                            icon={<ExternalLink size={16} />}
                            onClick={() => window.open(project.liveUrl, '_blank')}
                            className="flex-1"
                          >
                            Live Demo
                          </Button>
                        )}
                      </div>
                    </Card.Footer>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* View All Projects CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <Link href="/projects">
            <Button size="lg" icon={<ArrowRight size={20} />} iconPosition="right">
              View All Projects
            </Button>
          </Link>
        </motion.div>

        {/* Fun Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            {
              label: 'Projects Completed',
              value: projects.filter(p => p.status === 'completed').length,
            },
            { label: 'Technologies Used', value: '15+' },
            { label: 'Lines of Code', value: '10K+' },
            { label: 'Coffee Consumed', value: '∞' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">{stat.value}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}

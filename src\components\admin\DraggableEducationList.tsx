'use client';

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Edit, 
  Trash2, 
  GripVertical,
  AlertCircle,
  CheckCircle,
  Undo2,
  GraduationCap,
} from 'lucide-react';
import { useState, useEffect } from 'react';

import { ApiService } from '@/lib/api-service';
import type { Education } from '@/types';

interface DraggableEducationCardProps {
  education: Education;
  onEdit: (education: Education) => void;
  onDelete: (educationId: string) => void;
  isDragging?: boolean;
}

function DraggableEducationCard({ education, onEdit, onDelete, isDragging = false }: DraggableEducationCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: education.id! });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200 ${
        isSortableDragging ? 'shadow-2xl scale-105 z-50' : 'hover:shadow-lg'
      } ${isDragging ? 'ring-2 ring-primary-500 ring-opacity-50' : ''}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg z-10 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        title="Drag to reorder education"
        aria-label={`Drag to reorder ${education.degree} from ${education.institution}`}
        role="button"
        tabIndex={0}
      >
        <GripVertical className="w-4 h-4 text-gray-400 dark:text-gray-500" />
      </div>

      <div className="p-6 pl-12">
        <div className="flex items-start justify-between mb-2">
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate mb-1">
              {education.degree}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              {education.field}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {education.institution}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              {education.duration}
            </p>
            {education.gpa && (
              <p className="text-sm text-gray-500 dark:text-gray-500">
                GPA: {education.gpa}
              </p>
            )}
          </div>

          <div className="flex items-start space-x-2 ml-4">
            <button
              onClick={() => onEdit(education)}
              className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
              title="Edit education"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(education.id!)}
              className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
              title="Delete education"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {education.achievements && education.achievements.length > 0 && (
          <div className="mb-3">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Achievements:</p>
            {education.achievements.slice(0, 2).map((achievement, index) => (
              <p key={index} className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                • {achievement}
              </p>
            ))}
            {education.achievements.length > 2 && (
              <p className="text-sm text-gray-500 dark:text-gray-500">
                +{education.achievements.length - 2} more achievements
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

interface DraggableEducationListProps {
  education: Education[];
  onEdit: (education: Education) => void;
  onDelete: (educationId: string) => void;
  onReorder: (educationIds: string[]) => Promise<void>;
}

export default function DraggableEducationList({ 
  education, 
  onEdit, 
  onDelete, 
  onReorder 
}: DraggableEducationListProps) {
  const [items, setItems] = useState<Education[]>(education);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [isReordering, setIsReordering] = useState(false);
  const [reorderError, setReorderError] = useState<string | null>(null);
  const [reorderSuccess, setReorderSuccess] = useState(false);
  const [previousOrder, setPreviousOrder] = useState<Education[]>([]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setItems(education);
  }, [education]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id);
    setPreviousOrder([...items]);
    setReorderError(null);
    setReorderSuccess(false);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over?.id);

      const newItems = arrayMove(items, oldIndex, newIndex);
      setItems(newItems);

      // Save the new order to the backend
      setIsReordering(true);
      try {
        await onReorder(newItems.map(item => item.id!));
        setReorderSuccess(true);
        setTimeout(() => setReorderSuccess(false), 3000);
      } catch (error) {
        console.error('Failed to reorder education:', error);
        setReorderError('Failed to save new order. Please try again.');
        // Revert to previous order on error
        setItems(previousOrder);
        setTimeout(() => setReorderError(null), 5000);
      } finally {
        setIsReordering(false);
      }
    }

    setActiveId(null);
  };

  const handleUndo = () => {
    if (previousOrder.length > 0) {
      setItems(previousOrder);
      onReorder(previousOrder.map(item => item.id!)).catch(console.error);
      setPreviousOrder([]);
      setReorderError(null);
    }
  };

  const activeEducation = activeId ? items.find(item => item.id === activeId) : null;

  return (
    <div className="space-y-4">
      {/* Helper Text */}
      {items.length > 1 && (
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
          <GripVertical className="w-4 h-4 text-green-500" />
          <span>
            Drag the grip handle on the left of each education card to reorder your education. 
            The new order will be saved automatically and reflected on your public portfolio.
          </span>
        </div>
      )}

      {/* Status Messages */}
      <AnimatePresence>
        {isReordering && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-green-600 dark:text-green-400 text-sm"
          >
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 dark:border-green-400"></div>
            <span>Saving new order...</span>
          </motion.div>
        )}

        {reorderSuccess && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-green-600 dark:text-green-400 text-sm"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Education reordered successfully!</span>
          </motion.div>
        )}

        {reorderError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center justify-between bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3"
          >
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{reorderError}</span>
            </div>
            {previousOrder.length > 0 && (
              <button
                onClick={handleUndo}
                className="flex items-center space-x-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium"
              >
                <Undo2 className="w-3 h-3" />
                <span>Undo</span>
              </button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items.map(item => item.id!)} strategy={verticalListSortingStrategy}>
          <div className="space-y-4">
            {items.map((edu) => (
              <DraggableEducationCard
                key={edu.id}
                education={edu}
                onEdit={onEdit}
                onDelete={onDelete}
                isDragging={activeId === edu.id}
              />
            ))}
          </div>
        </SortableContext>

        <DragOverlay>
          {activeEducation ? (
            <DraggableEducationCard
              education={activeEducation}
              onEdit={onEdit}
              onDelete={onDelete}
              isDragging={true}
            />
          ) : null}
        </DragOverlay>
      </DndContext>

      {items.length === 0 && (
        <div className="text-center py-12 text-gray-500 dark:text-gray-400">
          <GraduationCap className="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
          <p>No education found. Add your first education to get started!</p>
        </div>
      )}
    </div>
  );
}

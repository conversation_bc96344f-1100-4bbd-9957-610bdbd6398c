'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, Github, Filter, Search, Calendar, Tag, Star, GitFork } from 'lucide-react';
import Image from 'next/image';
import React, { useState, useEffect } from 'react';

// import { projects } from '@/data/portfolio';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { ApiService } from '@/lib/api-service';

const categories = ['all', 'frontend', 'backend', 'fullstack', 'mobile', 'other'];

export default function ProjectsPage() {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedYear, setSelectedYear] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'year' | 'title'>('year');

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await ApiService.getPortfolio();
        if (response.success && response.data) {
          setProjects(response.data.projects || []);
        } else {
          console.error('Failed to fetch projects:', response.error);
          setProjects([]);
        }
      } catch (error) {
        console.error('Failed to fetch projects:', error);
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const years = [
    'all',
    ...Array.from(new Set(projects.map(p => p.year?.toString() || '2024')))
      .sort()
      .reverse(),
  ];

  // Filter and sort projects
  const filteredProjects = projects
    .filter(project => {
      const matchesCategory = selectedCategory === 'all' || project.category === selectedCategory;
      const matchesYear = selectedYear === 'all' || project.year.toString() === selectedYear;
      const matchesSearch =
        searchQuery === '' ||
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.technologies.some(tech => tech.toLowerCase().includes(searchQuery.toLowerCase()));

      return matchesCategory && matchesYear && matchesSearch;
    })
    .sort((a, b) => {
      if (sortBy === 'year') {
        return b.year - a.year;
      }
      return a.title.localeCompare(b.title);
    });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
            My Projects
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            A collection of projects I've worked on, showcasing my skills and passion for
            development
          </p>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-12"
        >
          <Card className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={20}
                />
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Category Filter */}
              <div className="relative">
                <Filter
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={20}
                />
                <select
                  value={selectedCategory}
                  onChange={e => setSelectedCategory(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all'
                        ? 'All Categories'
                        : category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Year Filter */}
              <div className="relative">
                <Calendar
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={20}
                />
                <select
                  value={selectedYear}
                  onChange={e => setSelectedYear(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none"
                >
                  {years.map(year => (
                    <option key={year} value={year}>
                      {year === 'all' ? 'All Years' : year}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort */}
              <div className="relative">
                <Tag
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={20}
                />
                <select
                  value={sortBy}
                  onChange={e => setSortBy(e.target.value as 'year' | 'title')}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none"
                >
                  <option value="year">Sort by Year</option>
                  <option value="title">Sort by Title</option>
                </select>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Results Count */}
        {!loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-8"
          >
            <p className="text-gray-600 dark:text-gray-400">
              Showing {filteredProjects.length} of {projects.length} projects
            </p>
          </motion.div>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="text-center py-20">
            <div className="animate-pulse text-gray-500 dark:text-gray-400 text-lg">
              Loading projects...
            </div>
          </div>
        ) : projects.length === 0 ? (
          /* Empty State */
          <div className="text-center py-20">
            <div className="max-w-md mx-auto">
              <div className="text-6xl mb-4">📁</div>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                No Projects Yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Start building your portfolio by adding your first project through the admin panel.
              </p>
            </div>
          </div>
        ) : filteredProjects.length === 0 ? (
          /* No Results State */
          <div className="text-center py-20">
            <div className="max-w-md mx-auto">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                No Projects Found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search criteria or filters to find what you're looking for.
              </p>
            </div>
          </div>
        ) : (
          /* Projects Grid */
          <AnimatePresence mode="wait">
            <motion.div
              key={`${selectedCategory}-${selectedYear}-${searchQuery}-${sortBy}`}
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredProjects.map(project => (
                <motion.div key={project.id} variants={itemVariants}>
                  <Card hover className="h-full overflow-hidden group" padding="none">
                    {/* Project Image */}
                    <div className="relative h-48 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 overflow-hidden">
                      {(project.imageUrl || project.image) ? (
                        <Image
                          src={project.imageUrl || project.image}
                          alt={project.title}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-6xl font-bold text-primary-200 dark:text-primary-800 opacity-50">
                            {project.title.charAt(0)}
                          </div>
                        </div>
                      )}

                      {/* Overlay on hover */}
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                        {project.githubUrl && (
                          <motion.a
                            href={project.githubUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                            aria-label="View source code"
                          >
                            <Github size={20} />
                          </motion.a>
                        )}

                        {project.liveUrl && (
                          <motion.a
                            href={project.liveUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                            aria-label="View live demo"
                          >
                            <ExternalLink size={20} />
                          </motion.a>
                        )}
                      </div>

                      {/* Featured Badge */}
                      {project.featured && (
                        <div className="absolute top-4 left-4 bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                          Featured
                        </div>
                      )}

                      {/* Status Badge */}
                      <div className="absolute top-4 right-4">
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            project.status === 'completed'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : project.status === 'in-progress'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                                : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                          }`}
                        >
                          {project.status.replace('-', ' ')}
                        </div>
                      </div>
                    </div>

                    <div className="p-6">
                      <Card.Header>
                        <div className="flex items-start justify-between mb-3">
                          <Card.Title className="group-hover:text-primary-500 transition-colors">
                            {project.title}
                          </Card.Title>
                          <span className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                            {project.year}
                          </span>
                        </div>
                        <Card.Description className="mb-4">{project.description}</Card.Description>
                      </Card.Header>

                      <Card.Content>
                        {/* Technologies */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {project.technologies.slice(0, 3).map(tech => (
                            <span
                              key={tech}
                              className="px-3 py-1 text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full"
                            >
                              {tech}
                            </span>
                          ))}
                          {project.technologies.length > 3 && (
                            <span className="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
                              +{project.technologies.length - 3} more
                            </span>
                          )}
                        </div>

                        {/* GitHub Stats */}
                        {project.githubData && (
                          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-4">
                            <div className="flex items-center space-x-1">
                              <Star className="w-3 h-3" />
                              <span>{project.githubData.stars}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <GitFork className="w-3 h-3" />
                              <span>{project.githubData.forks}</span>
                            </div>
                            {project.githubData.language && (
                              <div className="flex items-center space-x-1">
                                <div className="w-2 h-2 rounded-full bg-primary-500" />
                                <span>{project.githubData.language}</span>
                              </div>
                            )}
                            {project.syncedFromGithub && (
                              <div className="flex items-center space-x-1 text-green-500">
                                <Github className="w-3 h-3" />
                                <span>Synced</span>
                              </div>
                            )}
                          </div>
                        )}
                      </Card.Content>

                      <Card.Footer>
                        <div className="flex space-x-3">
                          {project.githubUrl && (
                            <Button
                              variant="outline"
                              size="sm"
                              icon={<Github size={16} />}
                              onClick={() => window.open(project.githubUrl, '_blank')}
                              className="flex-1"
                            >
                              Code
                            </Button>
                          )}

                          {project.liveUrl && (
                            <Button
                              size="sm"
                              icon={<ExternalLink size={16} />}
                              onClick={() => window.open(project.liveUrl, '_blank')}
                              className="flex-1"
                            >
                              Demo
                            </Button>
                          )}
                        </div>
                      </Card.Footer>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    </div>
  );
}

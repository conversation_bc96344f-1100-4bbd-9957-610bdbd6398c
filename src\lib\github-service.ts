import type { GitHubRepository, G<PERSON><PERSON><PERSON><PERSON><PERSON>, GitHubSyncConfig, GitHubSyncResult, Project } from '@/types';

export class GitHubService {
  private static readonly BASE_URL = 'https://api.github.com';
  private static readonly DEFAULT_PER_PAGE = 100;

  /**
   * Fetch user repositories from GitHub API
   */
  static async fetchUserRepositories(
    username: string,
    accessToken?: string,
    options: {
      includeForked?: boolean;
      includePrivate?: boolean;
      sort?: 'created' | 'updated' | 'pushed' | 'full_name';
      direction?: 'asc' | 'desc';
    } = {}
  ): Promise<GitHubRepository[]> {
    const { includeForked = false, includePrivate = false, sort = 'updated', direction = 'desc' } = options;
    
    const headers: Record<string, string> = {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': 'Portfolio-App',
    };

    if (accessToken) {
      headers['Authorization'] = `token ${accessToken}`;
    }

    const params = new URLSearchParams({
      per_page: this.DEFAULT_PER_PAGE.toString(),
      sort,
      direction,
    });

    try {
      const response = await fetch(`${this.BASE_URL}/users/${username}/repos?${params}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }

      const repositories: GitHubRepository[] = await response.json();

      // Filter repositories based on options
      return repositories.filter(repo => {
        if (!includeForked && repo.fork) return false;
        if (!includePrivate && repo.private) return false;
        if (repo.archived || repo.disabled) return false;
        return true;
      });
    } catch (error) {
      console.error('Error fetching GitHub repositories:', error);
      throw error;
    }
  }

  /**
   * Fetch user information from GitHub API
   */
  static async fetchUser(username: string, accessToken?: string): Promise<GitHubUser> {
    const headers: Record<string, string> = {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': 'Portfolio-App',
    };

    if (accessToken) {
      headers['Authorization'] = `token ${accessToken}`;
    }

    try {
      const response = await fetch(`${this.BASE_URL}/users/${username}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching GitHub user:', error);
      throw error;
    }
  }

  /**
   * Transform GitHub repository to Project format
   */
  static transformRepositoryToProject(repo: GitHubRepository): Omit<Project, 'id'> {
    // Determine category based on language and topics
    const category = this.determineProjectCategory(repo.language, repo.topics);
    
    // Generate technologies array from language and topics
    const technologies = this.extractTechnologies(repo.language, repo.topics);

    // Determine project status based on recent activity
    const status = this.determineProjectStatus(repo.updated_at, repo.pushed_at);

    return {
      title: repo.name.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: repo.description || `A ${repo.language || 'software'} project`,
      longDescription: repo.description || undefined,
      technologies,
      category,
      image: `/api/github/repo-image/${repo.full_name}`, // We'll create this endpoint
      imageUrl: `/api/github/repo-image/${repo.full_name}`,
      githubUrl: repo.html_url,
      liveUrl: repo.homepage || (repo.has_pages ? `https://${repo.full_name.split('/')[0]}.github.io/${repo.name}` : undefined),
      featured: repo.stargazers_count > 5 || repo.forks_count > 2, // Auto-feature popular repos
      year: new Date(repo.created_at).getFullYear(),
      status,
      githubData: {
        id: repo.id,
        name: repo.name,
        fullName: repo.full_name,
        stars: repo.stargazers_count,
        forks: repo.forks_count,
        language: repo.language,
        topics: repo.topics,
        lastUpdated: repo.updated_at,
        createdAt: repo.created_at,
        size: repo.size,
        openIssues: repo.open_issues_count,
        hasPages: repo.has_pages,
        homepage: repo.homepage,
        archived: repo.archived,
        disabled: repo.disabled,
        private: repo.private,
      },
      syncedFromGithub: true,
      githubSyncedAt: new Date().toISOString(),
    };
  }

  /**
   * Determine project category based on language and topics
   */
  private static determineProjectCategory(
    language: string | null,
    topics: string[]
  ): Project['category'] {
    const topicsLower = topics.map(t => t.toLowerCase());
    const lang = language?.toLowerCase();

    // Check topics first for more specific categorization
    if (topicsLower.some(t => ['mobile', 'android', 'ios', 'react-native', 'flutter'].includes(t))) {
      return 'mobile';
    }
    
    if (topicsLower.some(t => ['frontend', 'react', 'vue', 'angular', 'svelte', 'nextjs'].includes(t))) {
      return 'frontend';
    }
    
    if (topicsLower.some(t => ['backend', 'api', 'server', 'nodejs', 'express', 'fastapi'].includes(t))) {
      return 'backend';
    }
    
    if (topicsLower.some(t => ['fullstack', 'full-stack', 'web-app', 'webapp'].includes(t))) {
      return 'fullstack';
    }

    // Fallback to language-based categorization
    switch (lang) {
      case 'javascript':
      case 'typescript':
        return topicsLower.includes('nodejs') ? 'backend' : 'frontend';
      case 'python':
      case 'java':
      case 'go':
      case 'rust':
      case 'c#':
      case 'php':
        return 'backend';
      case 'html':
      case 'css':
      case 'scss':
      case 'sass':
        return 'frontend';
      case 'swift':
      case 'kotlin':
      case 'dart':
        return 'mobile';
      default:
        return 'other';
    }
  }

  /**
   * Extract technologies from language and topics
   */
  private static extractTechnologies(language: string | null, topics: string[]): string[] {
    const technologies = new Set<string>();

    // Add primary language
    if (language) {
      technologies.add(language);
    }

    // Add relevant topics as technologies
    const techTopics = topics.filter(topic => {
      const t = topic.toLowerCase();
      return !['portfolio', 'project', 'demo', 'example', 'tutorial', 'learning'].includes(t);
    });

    techTopics.forEach(topic => technologies.add(topic));

    return Array.from(technologies).slice(0, 8); // Limit to 8 technologies
  }

  /**
   * Determine project status based on activity
   */
  private static determineProjectStatus(updatedAt: string, pushedAt: string): Project['status'] {
    const now = new Date();
    const lastUpdate = new Date(Math.max(new Date(updatedAt).getTime(), new Date(pushedAt).getTime()));
    const daysSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSinceUpdate < 30) {
      return 'in-progress';
    } else if (daysSinceUpdate < 180) {
      return 'completed';
    } else {
      return 'completed'; // Old projects are considered completed
    }
  }

  /**
   * Validate GitHub username
   */
  static validateUsername(username: string): boolean {
    const githubUsernameRegex = /^[a-z\d](?:[a-z\d]|-(?=[a-z\d])){0,38}$/i;
    return githubUsernameRegex.test(username);
  }

  /**
   * Validate GitHub access token format
   */
  static validateAccessToken(token: string): boolean {
    // GitHub personal access tokens start with 'ghp_' and are 40 characters long
    // GitHub app tokens start with 'ghs_' and are 40 characters long
    // Classic tokens are 40 characters of hex
    return /^(ghp_[a-zA-Z0-9]{36}|ghs_[a-zA-Z0-9]{36}|[a-f0-9]{40})$/.test(token);
  }
}

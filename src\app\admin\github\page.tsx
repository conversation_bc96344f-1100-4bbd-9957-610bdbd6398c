'use client';

import { motion } from 'framer-motion';
import { Github, Refresh<PERSON><PERSON>, Setting<PERSON>, Star, GitFork, Calendar, ExternalLink, Check, X, Clock, Zap } from 'lucide-react';
import { useState, useEffect } from 'react';

import AdminProtectedLayout from '@/components/admin/AdminProtectedLayout';
import ErrorMessage from '@/components/admin/ErrorMessage';
import LoadingSpinner from '@/components/admin/LoadingSpinner';
import { ApiService } from '@/lib/api-service';
import type { GitHubRepository, GitHubSyncResult } from '@/types';

export default function GitHubSyncPage() {
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [selectedRepos, setSelectedRepos] = useState<string[]>([]);
  const [syncResult, setSyncResult] = useState<GitHubSyncResult | null>(null);

  const [config, setConfig] = useState({
    username: '',
    accessToken: '',
    includeForked: false,
    includePrivate: false,
    autoSync: false,
    syncInterval: 24,
  });

  const [autoSyncStatus, setAutoSyncStatus] = useState<any>(null);
  const [savingConfig, setSavingConfig] = useState(false);

  // Load configuration on mount
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const response = await ApiService.getGitHubConfig();
        if (response.success && response.data) {
          setConfig({
            username: response.data.config.username || '',
            accessToken: '', // Don't load the actual token for security
            includeForked: response.data.config.includeForked || false,
            includePrivate: response.data.config.includePrivate || false,
            autoSync: response.data.config.autoSync || false,
            syncInterval: response.data.config.syncInterval || 24,
          });
        }
      } catch (error) {
        console.error('Error loading GitHub config:', error);
      }
    };

    const loadAutoSyncStatus = async () => {
      try {
        const response = await ApiService.getAutoSyncStatus();
        if (response.success && response.data) {
          setAutoSyncStatus(response.data);
        }
      } catch (error) {
        console.error('Error loading auto-sync status:', error);
      }
    };

    loadConfig();
    loadAutoSyncStatus();
  }, []);

  const saveConfiguration = async () => {
    setSavingConfig(true);
    setError('');

    try {
      const response = await ApiService.updateGitHubConfig(config);
      if (response.success) {
        setSuccess('Configuration saved successfully!');
        // Reload auto-sync status
        const statusResponse = await ApiService.getAutoSyncStatus();
        if (statusResponse.success && statusResponse.data) {
          setAutoSyncStatus(statusResponse.data);
        }
      } else {
        setError(response.error || 'Failed to save configuration');
      }
    } catch (error) {
      console.error('Error saving configuration:', error);
      setError('Network error. Please try again.');
    } finally {
      setSavingConfig(false);
    }
  };

  const triggerAutoSync = async () => {
    setSyncing(true);
    setError('');
    setSuccess('');

    try {
      const response = await ApiService.triggerAutoSync();
      if (response.success && response.data) {
        setSyncResult(response.data);
        setSuccess('Auto-sync completed successfully!');
        // Reload auto-sync status
        const statusResponse = await ApiService.getAutoSyncStatus();
        if (statusResponse.success && statusResponse.data) {
          setAutoSyncStatus(statusResponse.data);
        }
      } else {
        setError(response.error || 'Failed to trigger auto-sync');
      }
    } catch (error) {
      console.error('Error triggering auto-sync:', error);
      setError('Network error. Please try again.');
    } finally {
      setSyncing(false);
    }
  };

  const fetchRepositories = async () => {
    if (!config.username.trim()) {
      setError('Please enter a GitHub username');
      return;
    }

    setLoading(true);
    setError('');
    setRepositories([]);

    try {
      const response = await ApiService.fetchGitHubRepositories({
        username: config.username.trim(),
        accessToken: config.accessToken.trim() || undefined,
        includeForked: config.includeForked,
        includePrivate: config.includePrivate,
      });

      if (response.success && response.data) {
        setRepositories(response.data.repositories);
        setSelectedRepos(response.data.repositories.map(repo => repo.name));
      } else {
        setError(response.error || 'Failed to fetch repositories');
      }
    } catch (error) {
      console.error('Error fetching repositories:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const syncProjects = async () => {
    if (!config.username.trim()) {
      setError('Please enter a GitHub username');
      return;
    }

    if (selectedRepos.length === 0) {
      setError('Please select at least one repository to sync');
      return;
    }

    setSyncing(true);
    setError('');
    setSuccess('');
    setSyncResult(null);

    try {
      const response = await ApiService.syncGitHubProjects({
        username: config.username.trim(),
        accessToken: config.accessToken.trim() || undefined,
        includeForked: config.includeForked,
        includePrivate: config.includePrivate,
        selectedRepos,
      });

      if (response.success && response.data) {
        setSyncResult(response.data);
        setSuccess(`Successfully synced ${response.data.syncedCount} repositories!`);
      } else {
        setError(response.error || 'Failed to sync repositories');
      }
    } catch (error) {
      console.error('Error syncing repositories:', error);
      setError('Network error. Please try again.');
    } finally {
      setSyncing(false);
    }
  };

  const toggleRepoSelection = (repoName: string) => {
    setSelectedRepos(prev =>
      prev.includes(repoName)
        ? prev.filter(name => name !== repoName)
        : [...prev, repoName]
    );
  };

  const selectAllRepos = () => {
    setSelectedRepos(repositories.map(repo => repo.name));
  };

  const deselectAllRepos = () => {
    setSelectedRepos([]);
  };

  return (
    <AdminProtectedLayout title="GitHub Integration" subtitle="Sync your GitHub repositories as portfolio projects">
      <div className="p-6 max-w-6xl mx-auto">
        {/* Configuration Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-surface-light dark:bg-surface-dark rounded-2xl border border-border-light dark:border-border-dark p-6 mb-8"
        >
          <div className="flex items-center mb-6">
            <Github className="w-6 h-6 text-primary-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">GitHub Configuration</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                GitHub Username *
              </label>
              <input
                type="text"
                value={config.username}
                onChange={(e) => setConfig({ ...config, username: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="your-github-username"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Access Token (Optional)
              </label>
              <input
                type="password"
                value={config.accessToken}
                onChange={(e) => setConfig({ ...config, accessToken: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Required for private repositories and higher rate limits
              </p>
            </div>
          </div>

          <div className="flex flex-wrap gap-4 mt-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.includeForked}
                onChange={(e) => setConfig({ ...config, includeForked: e.target.checked })}
                className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include forked repositories</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.includePrivate}
                onChange={(e) => setConfig({ ...config, includePrivate: e.target.checked })}
                className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include private repositories</span>
            </label>
          </div>

          <div className="flex gap-3 mt-6">
            <button
              onClick={fetchRepositories}
              disabled={loading || !config.username.trim()}
              className="inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              Fetch Repositories
            </button>
            <button
              onClick={saveConfiguration}
              disabled={savingConfig}
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {savingConfig ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Settings className="w-4 h-4 mr-2" />
              )}
              Save Config
            </button>
          </div>
        </motion.div>

        {/* Auto-Sync Configuration */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-surface-light dark:bg-surface-dark rounded-2xl border border-border-light dark:border-border-dark p-6 mb-8"
        >
          <div className="flex items-center mb-6">
            <Clock className="w-6 h-6 text-primary-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Auto-Sync Settings</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.autoSync}
                  onChange={(e) => setConfig({ ...config, autoSync: e.target.checked })}
                  className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Enable automatic sync
                </span>
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Automatically sync projects from GitHub at regular intervals
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Sync Interval (hours)
              </label>
              <input
                type="number"
                min="1"
                max="168"
                value={config.syncInterval}
                onChange={(e) => setConfig({ ...config, syncInterval: parseInt(e.target.value) || 24 })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                disabled={!config.autoSync}
              />
            </div>
          </div>

          {autoSyncStatus && (
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                  <span className={`ml-2 ${config.autoSync ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'}`}>
                    {config.autoSync ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
                {autoSyncStatus.lastSyncAt && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Last Sync:</span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {new Date(autoSyncStatus.lastSyncAt).toLocaleString()}
                    </span>
                  </div>
                )}
                {autoSyncStatus.nextSyncAt && config.autoSync && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Next Sync:</span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {new Date(autoSyncStatus.nextSyncAt).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="flex gap-3 mt-6">
            <button
              onClick={triggerAutoSync}
              disabled={syncing || !config.username.trim() || !autoSyncStatus?.canSyncNow}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {syncing ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Zap className="w-4 h-4 mr-2" />
              )}
              Trigger Auto-Sync
            </button>
          </div>
        </motion.div>

        {/* Error/Success Messages */}
        {error && (
          <ErrorMessage
            message={error}
            onDismiss={() => setError('')}
            className="mb-6"
          />
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6"
          >
            <div className="flex items-center">
              <Check className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <p className="text-green-700 dark:text-green-300">{success}</p>
            </div>
          </motion.div>
        )}

        {/* Sync Result */}
        {syncResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-surface-light dark:bg-surface-dark rounded-2xl border border-border-light dark:border-border-dark p-6 mb-8"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sync Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">{syncResult.syncedCount}</div>
                <div className="text-sm text-green-700 dark:text-green-300">Synced</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{syncResult.skippedCount}</div>
                <div className="text-sm text-yellow-700 dark:text-yellow-300">Skipped</div>
              </div>
              <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">{syncResult.errorCount}</div>
                <div className="text-sm text-red-700 dark:text-red-300">Errors</div>
              </div>
            </div>
            {syncResult.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Errors:</h4>
                <ul className="text-sm text-red-600 dark:text-red-400 space-y-1">
                  {syncResult.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </motion.div>
        )}

        {/* Repository Selection */}
        {repositories.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-surface-light dark:bg-surface-dark rounded-2xl border border-border-light dark:border-border-dark p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Select Repositories ({repositories.length} found)
              </h2>
              <div className="flex gap-2">
                <button
                  onClick={selectAllRepos}
                  className="text-sm text-primary-500 hover:text-primary-600 transition-colors"
                >
                  Select All
                </button>
                <span className="text-gray-300 dark:text-gray-600">|</span>
                <button
                  onClick={deselectAllRepos}
                  className="text-sm text-primary-500 hover:text-primary-600 transition-colors"
                >
                  Deselect All
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {repositories.map((repo) => (
                <motion.div
                  key={repo.id}
                  whileHover={{ scale: 1.02 }}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedRepos.includes(repo.name)
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => toggleRepoSelection(repo.name)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium text-gray-900 dark:text-white truncate">{repo.name}</h3>
                    {selectedRepos.includes(repo.name) ? (
                      <Check className="w-5 h-5 text-primary-500 flex-shrink-0" />
                    ) : (
                      <div className="w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded flex-shrink-0" />
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {repo.description || 'No description available'}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-3">
                      {repo.language && (
                        <span className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-primary-500 mr-1" />
                          {repo.language}
                        </span>
                      )}
                      <span className="flex items-center">
                        <Star className="w-3 h-3 mr-1" />
                        {repo.stargazers_count}
                      </span>
                      <span className="flex items-center">
                        <GitFork className="w-3 h-3 mr-1" />
                        {repo.forks_count}
                      </span>
                    </div>
                    <a
                      href={repo.html_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-500 hover:text-primary-600 transition-colors"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {selectedRepos.length} of {repositories.length} repositories selected
              </p>
              <button
                onClick={syncProjects}
                disabled={syncing || selectedRepos.length === 0}
                className="inline-flex items-center px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {syncing ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <Github className="w-4 h-4 mr-2" />
                )}
                Sync Selected Projects
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </AdminProtectedLayout>
  );
}

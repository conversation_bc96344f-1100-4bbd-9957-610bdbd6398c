'use client';

import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import {
  Code2,
  Database,
  Wrench,
  Palette,
  Languages,
  MoreHorizontal,
  Star,
  Zap,
  Sparkles,
  Award,
  Target,
  TrendingUp,
  Brain,
  Bo<PERSON>,
} from 'lucide-react';
import React, { useState, useEffect } from 'react';

// import { skills } from '@/data/portfolio';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { ApiService } from '@/lib/api-service';
import type { Skill } from '@/types';

const categoryIcons = {
  frontend: Palette,
  backend: Code2,
  database: Database,
  tools: Wrench,
  languages: Languages,
  ai: Brain,
  'machine-learning': Bot,
  other: MoreHorizontal,
};

const categoryColors = {
  frontend: {
    bg: 'bg-pink-500/10',
    text: 'text-pink-500',
    border: 'border-pink-500/30',
    glow: 'shadow-pink-500/25',
    gradient: 'from-pink-500/20 to-purple-500/20',
    accent: 'bg-pink-500',
    accentLight: 'bg-pink-300',
    accentDark: 'bg-pink-700',
    hue: 'pink',
  },
  backend: {
    bg: 'bg-blue-500/10',
    text: 'text-blue-500',
    border: 'border-blue-500/30',
    glow: 'shadow-blue-500/25',
    gradient: 'from-blue-500/20 to-cyan-500/20',
    accent: 'bg-blue-500',
    accentLight: 'bg-blue-300',
    accentDark: 'bg-blue-700',
    hue: 'blue',
  },
  database: {
    bg: 'bg-green-500/10',
    text: 'text-green-500',
    border: 'border-green-500/30',
    glow: 'shadow-green-500/25',
    gradient: 'from-green-500/20 to-emerald-500/20',
    accent: 'bg-green-500',
    accentLight: 'bg-green-300',
    accentDark: 'bg-green-700',
    hue: 'green',
  },
  tools: {
    bg: 'bg-orange-500/10',
    text: 'text-orange-500',
    border: 'border-orange-500/30',
    glow: 'shadow-orange-500/25',
    gradient: 'from-orange-500/20 to-yellow-500/20',
    accent: 'bg-orange-500',
    accentLight: 'bg-orange-300',
    accentDark: 'bg-orange-700',
    hue: 'orange',
  },
  languages: {
    bg: 'bg-purple-500/10',
    text: 'text-purple-500',
    border: 'border-purple-500/30',
    glow: 'shadow-purple-500/25',
    gradient: 'from-purple-500/20 to-indigo-500/20',
    accent: 'bg-purple-500',
    accentLight: 'bg-purple-300',
    accentDark: 'bg-purple-700',
    hue: 'purple',
  },
  ai: {
    bg: 'bg-violet-500/10',
    text: 'text-violet-500',
    border: 'border-violet-500/30',
    glow: 'shadow-violet-500/25',
    gradient: 'from-violet-500/20 to-purple-500/20',
    accent: 'bg-violet-500',
    accentLight: 'bg-violet-300',
    accentDark: 'bg-violet-700',
    hue: 'violet',
  },
  'machine-learning': {
    bg: 'bg-emerald-500/10',
    text: 'text-emerald-500',
    border: 'border-emerald-500/30',
    glow: 'shadow-emerald-500/25',
    gradient: 'from-emerald-500/20 to-teal-500/20',
    accent: 'bg-emerald-500',
    accentLight: 'bg-emerald-300',
    accentDark: 'bg-emerald-700',
    hue: 'emerald',
  },
  other: {
    bg: 'bg-gray-500/10',
    text: 'text-gray-500',
    border: 'border-gray-500/30',
    glow: 'shadow-gray-500/25',
    gradient: 'from-gray-500/20 to-slate-500/20',
    accent: 'bg-gray-500',
    accentLight: 'bg-gray-300',
    accentDark: 'bg-gray-700',
    hue: 'gray',
  },
};

const levelColors = {
  beginner: 'bg-yellow-500',
  intermediate: 'bg-blue-500',
  advanced: 'bg-green-500',
  expert: 'bg-purple-500',
};

const levelIcons = {
  beginner: Target,
  intermediate: TrendingUp,
  advanced: Zap,
  expert: Award,
};

export default function SkillsGrid() {
  const [skills, setSkills] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        const response = await ApiService.getPortfolio();
        if (response.success && response.data) {
          setSkills(response.data.skills || []);
        } else {
          console.error('Failed to fetch skills:', response.error);
          setSkills([]);
        }
      } catch (error) {
        console.error('Failed to fetch skills:', error);
        setSkills([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSkills();
  }, []);

  // Group skills by category
  const skillsByCategory = skills.reduce(
    (acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = [];
      }
      acc[skill.category].push(skill);
      return acc;
    },
    {} as Record<string, typeof skills>,
  );

  const categories = ['all', ...Object.keys(skillsByCategory)];

  const filteredSkills =
    selectedCategory === 'all' ? skills : skillsByCategory[selectedCategory] || [];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1 },
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-pulse text-gray-500 dark:text-gray-400">Loading skills...</div>
      </div>
    );
  }

  if (skills.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🛠️</div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          No Skills Added Yet
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Add your skills through the admin panel to showcase your expertise.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Category Filter */}
      <div className="flex flex-wrap justify-center gap-2">
        {categories.map(category => {
          const isActive = selectedCategory === category;
          const IconComponent =
            category === 'all'
              ? MoreHorizontal
              : categoryIcons[category as keyof typeof categoryIcons];

          return (
            <Button
              key={category}
              variant={isActive ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              icon={IconComponent ? <IconComponent size={16} /> : undefined}
              className="capitalize"
            >
              {category}
            </Button>
          );
        })}
      </div>

      {/* Skills Grid */}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedCategory}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4"
        >
          {filteredSkills.map((skill, index) => {
            const categoryColor = categoryColors[skill.category as keyof typeof categoryColors];
            const IconComponent = categoryIcons[skill.category as keyof typeof categoryIcons];
            const LevelIcon = levelIcons[skill.level as keyof typeof levelIcons];
            const levelValue = { beginner: 1, intermediate: 2, advanced: 3, expert: 4 }[
              skill.level
            ];

            return (
              <motion.div
                key={skill.name}
                variants={itemVariants}
                whileHover={{
                  scale: 1.05,
                  y: -8,
                  transition: { type: 'spring', stiffness: 300, damping: 20 },
                }}
                onHoverStart={() => setHoveredSkill(skill.name)}
                onHoverEnd={() => setHoveredSkill(null)}
                className="group"
              >
                <div
                  className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${categoryColor.gradient} backdrop-blur-sm border ${categoryColor.border} transition-all duration-500 group-hover:shadow-2xl group-hover:${categoryColor.glow} h-full`}
                >
                  {/* Animated background particles */}
                  <div className="absolute inset-0 overflow-hidden">
                    {[...Array(4)].map((_, i) => (
                      <motion.div
                        key={i}
                        className={`absolute w-1 h-1 ${categoryColor.text.replace('text-', 'bg-')} rounded-full opacity-20`}
                        style={{
                          left: `${20 + i * 20}%`,
                          top: `${20 + i * 15}%`,
                        }}
                        animate={{
                          y: [-8, 8, -8],
                          x: [-3, 3, -3],
                          opacity: [0.2, 0.5, 0.2],
                        }}
                        transition={{
                          duration: 2 + Math.random(),
                          repeat: Infinity,
                          delay: i * 0.3,
                        }}
                      />
                    ))}
                  </div>

                  {/* Glowing border effect on hover */}
                  <div
                    className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${categoryColor.gradient} opacity-0 group-hover:opacity-30 transition-opacity duration-500`}
                  />

                  <div className="relative p-5 h-full flex flex-col">
                    {/* Category indicator with floating animation */}
                    <motion.div
                      className={`absolute -top-2 -right-2 w-10 h-10 ${categoryColor.bg} ${categoryColor.border} border-2 rounded-full flex items-center justify-center backdrop-blur-sm`}
                      animate={{
                        y: [-2, 2, -2],
                        rotate: [0, 5, -5, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'easeInOut',
                      }}
                    >
                      <IconComponent size={16} className={`${categoryColor.text} drop-shadow-sm`} />
                    </motion.div>

                    {/* Skill name with typing effect */}
                    <motion.h3
                      className="font-bold text-lg text-gray-900 dark:text-white mb-3 pr-8"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 + index * 0.05 }}
                    >
                      {skill.name}
                    </motion.h3>

                    {/* Level indicator with icon and animated progress */}
                    <div className="space-y-3 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <motion.div
                            className={`p-1.5 rounded-lg ${categoryColor.bg} ${categoryColor.border} border`}
                            whileHover={{ scale: 1.1, rotate: 5 }}
                          >
                            <LevelIcon size={14} className={categoryColor.text} />
                          </motion.div>
                          <span className="text-sm font-semibold text-gray-700 dark:text-gray-300 capitalize">
                            {skill.level}
                          </span>
                        </div>

                        {skill.level === 'expert' && (
                          <motion.div
                            animate={{
                              rotate: 360,
                              scale: [1, 1.1, 1],
                            }}
                            transition={{
                              rotate: { duration: 3, repeat: Infinity, ease: 'linear' },
                              scale: { duration: 2, repeat: Infinity, ease: 'easeInOut' },
                            }}
                          >
                            <Star size={16} className={`${categoryColor.text} fill-current`} />
                          </motion.div>
                        )}
                      </div>

                      {/* Innovative circular progress indicator */}
                      <div className="relative">
                        <div className="flex justify-center mb-2">
                          <div className="relative w-16 h-16">
                            {/* Background circle */}
                            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                              <circle
                                cx="32"
                                cy="32"
                                r="28"
                                stroke="currentColor"
                                strokeWidth="4"
                                fill="none"
                                className="text-gray-200 dark:text-gray-700"
                              />
                              {/* Progress circle */}
                              <motion.circle
                                cx="32"
                                cy="32"
                                r="28"
                                stroke="currentColor"
                                strokeWidth="4"
                                fill="none"
                                className={categoryColor.text}
                                strokeLinecap="round"
                                strokeDasharray={`${2 * Math.PI * 28}`}
                                initial={{ strokeDashoffset: 2 * Math.PI * 28 }}
                                animate={{
                                  strokeDashoffset: 2 * Math.PI * 28 * (1 - levelValue / 4),
                                }}
                                transition={{
                                  duration: 1.5,
                                  delay: 0.5 + index * 0.1,
                                  ease: 'easeOut',
                                }}
                              />
                            </svg>

                            {/* Center percentage */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <motion.span
                                className="text-sm font-bold text-gray-800 dark:text-gray-200"
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 1 + index * 0.1 }}
                              >
                                {Math.round((levelValue / 4) * 100)}%
                              </motion.span>
                            </div>
                          </div>
                        </div>

                        {/* Level dots indicator */}
                        <div className="flex justify-center space-x-1">
                          {[1, 2, 3, 4].map(level => (
                            <motion.div
                              key={level}
                              className={`w-2 h-2 rounded-full ${
                                level <= levelValue
                                  ? `${categoryColor.text.replace('text-', 'bg-')} shadow-lg`
                                  : 'bg-gray-300 dark:bg-gray-600'
                              }`}
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{
                                delay: 1.2 + index * 0.05 + level * 0.1,
                                type: 'spring',
                                stiffness: 200,
                              }}
                              whileHover={{ scale: 1.3 }}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Skill mastery badge */}
                    <motion.div
                      className={`mt-3 inline-flex items-center space-x-1 px-2 py-1 rounded-full ${categoryColor.bg} ${categoryColor.border} border text-xs font-medium ${categoryColor.text}`}
                      whileHover={{ scale: 1.05 }}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1.8 + index * 0.05 }}
                    >
                      <Sparkles size={10} />
                      <span>{levelValue}/4 Mastery</span>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>
      </AnimatePresence>

      {/* Skills Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.5 }}
        className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-4 mt-12"
      >
        {Object.entries(skillsByCategory).map(([category, categorySkills]) => {
          const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
          const categoryColor = categoryColors[category as keyof typeof categoryColors];
          const skills = categorySkills as Skill[];

          return (
            <Card key={category} className="text-center">
              <div className={`inline-flex p-3 rounded-lg ${categoryColor.bg} mb-3`}>
                <IconComponent size={24} className={categoryColor.text} />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white capitalize mb-1">
                {category}
              </h3>
              <p className="text-2xl font-bold text-primary-500 dark:text-primary-400">
                {skills.length}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {skills.length === 1 ? 'skill' : 'skills'}
              </p>
            </Card>
          );
        })}
      </motion.div>
    </div>
  );
}

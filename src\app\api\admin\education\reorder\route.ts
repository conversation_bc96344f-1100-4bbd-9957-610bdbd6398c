import { NextRequest, NextResponse } from 'next/server';

import { getAuthenticatedUser } from '@/lib/auth-server';
import { getPortfolioData, savePortfolioData } from '@/lib/storage';
import type { Education } from '@/types';

export async function PUT(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { educationIds } = await request.json();

    if (!Array.isArray(educationIds)) {
      return NextResponse.json(
        { error: 'Education IDs must be an array' },
        { status: 400 }
      );
    }

    // Get current portfolio data
    const portfolioData = await getPortfolioData();
    const education = portfolioData.education || [];

    // Validate that all provided IDs exist
    const existingIds = new Set(education.map(e => e.id));
    const invalidIds = educationIds.filter(id => !existingIds.has(id));
    
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid education IDs: ${invalidIds.join(', ')}` },
        { status: 400 }
      );
    }

    // Check if all education items are included in the reorder
    if (educationIds.length !== education.length) {
      return NextResponse.json(
        { error: 'All education items must be included in the reorder' },
        { status: 400 }
      );
    }

    // Create a map of education by ID for efficient lookup
    const educationMap = new Map<string, Education>();
    education.forEach(edu => {
      educationMap.set(edu.id!, edu);
    });

    // Reorder education and assign new order values
    const reorderedEducation: Education[] = educationIds.map((id: string, index: number) => {
      const edu = educationMap.get(id)!;
      return {
        ...edu,
        order: index,
        updatedAt: new Date().toISOString(),
      };
    });

    // Save the reordered education
    const updatedPortfolioData = {
      ...portfolioData,
      education: reorderedEducation,
    };

    const saveResult = await savePortfolioData(updatedPortfolioData);

    if (!saveResult.success) {
      return NextResponse.json(
        { error: 'Failed to save education order' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Education reordered successfully',
      education: reorderedEducation,
    });
  } catch (error) {
    console.error('Error reordering education:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve education in their current order
export async function GET() {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const portfolioData = await getPortfolioData();
    const education = portfolioData.education || [];

    // Sort education by order field, with fallback to creation date
    const sortedEducation = [...education].sort((a, b) => {
      // If both have order, sort by order
      if (typeof a.order === 'number' && typeof b.order === 'number') {
        return a.order - b.order;
      }
      
      // If only one has order, prioritize it
      if (typeof a.order === 'number') return -1;
      if (typeof b.order === 'number') return 1;
      
      // If neither has order, sort by creation date (newest first)
      const aDate = new Date(a.createdAt || 0).getTime();
      const bDate = new Date(b.createdAt || 0).getTime();
      return bDate - aDate;
    });

    return NextResponse.json({
      success: true,
      education: sortedEducation,
    });
  } catch (error) {
    console.error('Error fetching ordered education:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
